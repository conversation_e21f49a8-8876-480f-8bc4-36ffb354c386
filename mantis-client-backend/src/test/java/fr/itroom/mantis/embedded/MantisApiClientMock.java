package fr.itroom.mantis.embedded;

import fr.itroom.mantis.domain.mantis.filter.Filter;
import fr.itroom.mantis.domain.mantis.issue.Issue;
import fr.itroom.mantis.domain.mantis.issue.attachment.Attachment;
import fr.itroom.mantis.domain.mantis.issue.note.Note;
import fr.itroom.mantis.domain.mantis.project.Project;
import fr.itroom.mantis.domain.mantis.response.CountResponse;
import fr.itroom.mantis.domain.mantis.user.MantisSessionUser;
import fr.itroom.mantis.domain.mantis.user.UserBase;
import fr.itroom.mantis.domain.web.request.IssuesFilter;
import fr.itroom.mantis.infrastructure.client.IMantisApiClient;
import fr.itroom.mantis.infrastructure.client.MantisApiClient;
import fr.itroom.mantis.infrastructure.exception.EntityNotFoundException;
import fr.itroom.mantis.infrastructure.exception.InvalidMantisSessionException;
import fr.itroom.mantis.infrastructure.exception.MantisException;
import jakarta.annotation.Nullable;
import lombok.Setter;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseCookie;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Profile("mantisApiMock")
public class MantisApiClientMock implements IMantisApiClient {

    @Nullable
    @Setter
    private MantisSessionUser mantisSessionUser;

    @Nullable
    @Setter
    private String mantisCreateSessionToken;

    @Override
    public UserBase getSession(String token) throws InvalidMantisSessionException {
        if (mantisSessionUser == null) {
            throw InvalidMantisSessionException.expired();
        }

        return mantisSessionUser;
    }

    @Override
    public ResponseCookie createSession(String username, String password) throws BadCredentialsException {
        if (mantisCreateSessionToken == null) {
            throw new BadCredentialsException("Failed to create session");
        }

        return ResponseCookie.from(MantisApiClient.MANTIS_SESSION_COOKIE_NAME, mantisCreateSessionToken).build();
    }

    @Override
    public void deleteSession(String token) {

    }

    @Override
    public List<Issue> getIssues(String token, IssuesFilter filter) throws InvalidMantisSessionException {
        return List.of();
    }

    @Override
    public Issue getIssue(String token, int issueId) throws InvalidMantisSessionException, EntityNotFoundException {
        return null;
    }

    @Override
    public Attachment getAttachment(String token, int issueId, int attachmentId) throws InvalidMantisSessionException, EntityNotFoundException {
        return null;
    }

    @Override
    public Issue createIssue(String token, Issue.CreateRequest createRequest) throws InvalidMantisSessionException, MantisException {
        return null;
    }

    @Override
    public void updateIssue(String token, int issueId, Issue.UpdateRequest updateRequest) throws MantisException {

    }

    @Override
    public void createIssueNote(String token, int issueId, Note.CreateRequest createRequest) throws InvalidMantisSessionException, MantisException {

    }

    @Override
    public void createAttachment(String token, int issueId, Attachment.CreateRequest createRequest) throws InvalidMantisSessionException, MantisException {

    }

    @Override
    public List<Filter> getFilters(String token) throws InvalidMantisSessionException {
        return List.of();
    }

    @Override
    public void forgotPassword(String username, String email) throws MantisException {

    }

    @Override
    public void resetPassword(int userId, String token, String password) throws MantisException {

    }

    @Override
    public List<Project> getProjects(String token) throws InvalidMantisSessionException, MantisException {
        return List.of();
    }

	@Override
	public CountResponse getCount(String token, IssuesFilter filter)
			throws InvalidMantisSessionException, MantisException {
				return new CountResponse(10, 1);
	}
}
