package fr.itroom.mantis.infrastructure.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import fr.itroom.mantis.domain.mantis.filter.Filter;
import fr.itroom.mantis.domain.mantis.issue.Issue;
import fr.itroom.mantis.domain.mantis.issue.attachment.Attachment;
import fr.itroom.mantis.domain.mantis.issue.note.Note;
import fr.itroom.mantis.domain.mantis.project.Project;
import fr.itroom.mantis.domain.mantis.response.*;
import fr.itroom.mantis.domain.mantis.user.UserBase;
import fr.itroom.mantis.domain.web.request.IssuesFilter;
import fr.itroom.mantis.infrastructure.config.MantisApiConfiguration;
import fr.itroom.mantis.infrastructure.exception.EntityNotFoundException;
import fr.itroom.mantis.infrastructure.exception.InvalidMantisSessionException;
import fr.itroom.mantis.infrastructure.exception.MantisException;
import jakarta.annotation.Nullable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseCookie;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Component
@Profile("!mantisApiMock")
public class MantisApiClient implements IMantisApiClient {

    public static final String MANTIS_SESSION_COOKIE_NAME = "MANTIS_STRING_COOKIE";
    private static final Logger LOGGER = LogManager.getLogger(MantisApiClient.class);
    private static final Consumer<HttpHeaders> formContentType =
            headers -> headers.set("Content-Type", "application/x-www-form-urlencoded");

    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final String baseUrl;

    public MantisApiClient(WebClient.Builder webClientBuilder, ObjectMapper objectMapper, MantisApiConfiguration mantisConfiguration) {
        this.webClient = webClientBuilder.baseUrl(mantisConfiguration.getBaseUrl())
                .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(100 * 1000 * 1024)).build();
        this.objectMapper = objectMapper;
        this.baseUrl = mantisConfiguration.getBaseUrl();
    }

    private UriBuilder newUriBuilder() {
        return UriComponentsBuilder.fromUriString(baseUrl);
    }

    /**
     * Récupère une réponse de l'API Mantis.
     *
     * @param method       méthode HTTP à utiliser
     * @param uri          URI de la requête
     * @param sessionToken token de session
     * @param bodyValue    corps de la requête (optionnel)
     * @param responseType type de la réponse (classe)
     * @param <T>          type de la réponse
     * @return la réponse de l'API
     * @throws MantisException si une erreur survient (réponse vide, impossible de lire la réponse)
     */
    private <T> T getResponse(HttpMethod method, URI uri, String sessionToken, @Nullable Object bodyValue, Class<T> responseType) throws MantisException {
        WebClient.RequestBodySpec spec = webClient.method(method)
                .uri(uri)
                .cookie(MANTIS_SESSION_COOKIE_NAME, sessionToken);

        if (bodyValue != null) {
            spec.bodyValue(bodyValue);
        }

        String json = spec.retrieve()
                .onStatus(
                        status -> status.is4xxClientError() || status.is5xxServerError(),
                        response -> {
                            if (response.statusCode().equals(HttpStatus.BAD_REQUEST)) {
                                LOGGER.warn("Bad request from Mantis API : {}", response.bodyToMono(String.class).block());
                                return Mono.error(new MantisException("Requête invalide."));
                            }

                            if (response.statusCode().equals(HttpStatus.INTERNAL_SERVER_ERROR)) {
                                return Mono.error(new MantisException("Erreur interne du serveur Mantis."));
                            }

                            if (response.statusCode().equals(HttpStatus.NOT_FOUND)) {
                                return Mono.error(EntityNotFoundException.ticket());
                            }

                            return Mono.error(InvalidMantisSessionException.expired());
                        }
                ).bodyToMono(String.class).block();

        if (json == null || json.isEmpty()) {
            throw new MantisException("Le serveur a renvoyé une réponse vide.");
        }

        T response;

        try {
            response = objectMapper.readValue(json, responseType);
        } catch (Exception e) {
            LOGGER.error("Failed to parse issues response", e);
            LOGGER.error("Raw JSON : {}", json);

            throw new MantisException("Impossible de lire la réponse du serveur.");
        }

        return response;
    }

    @Override
    public UserBase getSession(String token) throws InvalidMantisSessionException {
        UserBase mantisSessionUser = webClient
                .get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/rest/users/me")
                        .build())
                .cookie(MANTIS_SESSION_COOKIE_NAME, token)
                .retrieve()
                .onStatus(
                        status -> status.isSameCodeAs(HttpStatus.UNAUTHORIZED) || status.isSameCodeAs(HttpStatus.FORBIDDEN),
                        response -> Mono.error(InvalidMantisSessionException.expired()
                        ))
                .bodyToMono(UserBase.class).block();

        if (mantisSessionUser == null) {
            throw InvalidMantisSessionException.expired();
        }

        return mantisSessionUser;
    }

    @Override
    public ResponseCookie createSession(String username, String password) throws BadCredentialsException {
        ClientResponse response = webClient
                .post()
                .uri(uriBuilder -> uriBuilder
                        .path("/login.php")
                        .build())
                .headers(formContentType)

                .bodyValue("username=" + username + "&password=" + password)
                .exchangeToMono(Mono::just)
                .block();

        if (response == null || !response.statusCode().isSameCodeAs(HttpStatus.FOUND)) {
            throw new BadCredentialsException("Failed to login to Mantis");
        }

        MultiValueMap<String, ResponseCookie> responseCookies = response.cookies();

        if (!responseCookies.containsKey(MANTIS_SESSION_COOKIE_NAME)) {
            throw new BadCredentialsException("Les identifiants sont incorrects");
        }

        return responseCookies.get(MANTIS_SESSION_COOKIE_NAME).getFirst();
    }

    @Override
    public void deleteSession(String token) {
        webClient
                .get()
                .uri(uriBuilder -> uriBuilder
                        .path("/logout_page.php")
                        .build())
                .cookie(MANTIS_SESSION_COOKIE_NAME, token)
                .retrieve();
    }

    @Override
    public List<Issue> getIssues(String token, IssuesFilter filter) throws MantisException {
        UriBuilder uriBuilder = newUriBuilder().path("/api/rest/custom/issues")
                .queryParam("page", filter.getPage())
                .queryParam("page_size", filter.getSize());

        if (filter.hasProjectId()) {
            uriBuilder.queryParam("project_id", filter.getProjectId());
        }

        if (filter.hasFilterId()) {
            uriBuilder.queryParam("filter_id", filter.getFilterId());
        }

        if (filter.hasSort()){
            uriBuilder.queryParam("sort", filter.getSort());
        }

        if (filter.hasSummary()) {
            uriBuilder.queryParam("summary", filter.getSummary().toLowerCase());
        }

        IssuesResponse response = getResponse(HttpMethod.GET, uriBuilder.build(), token, null, IssuesResponse.class);
        return response.getIssues();
    }

    @Override
    public Issue getIssue(String token, int issueId) throws EntityNotFoundException, MantisException {
        IssuesResponse response = getResponse(HttpMethod.GET,
                newUriBuilder().path("/api/rest/issues/" + issueId).build(), token, null, IssuesResponse.class);

        if (response == null || response.getIssues().isEmpty()) {
            throw EntityNotFoundException.ticket();
        }

        return response.getIssues().getFirst();
    }

    @Override
    public Attachment getAttachment(String token, int issueId, int attachmentId) throws EntityNotFoundException, MantisException {
        AttachmentsResponse response = getResponse(HttpMethod.GET,
                newUriBuilder().path("/api/rest/issues/%s/files/%s".formatted(
                        issueId, attachmentId
                )).build(),
                token,
                null,
                AttachmentsResponse.class);

        if (response == null || response.getFiles().isEmpty()) {
            throw EntityNotFoundException.ticket();
        }

        return response.getFiles().getFirst();
    }

    @Override
    public Issue createIssue(String token, Issue.CreateRequest createRequest) throws MantisException {
        IssueResponse response = getResponse(
                HttpMethod.POST,
                newUriBuilder().path("/api/rest/issues").build(),
                token,
                createRequest,
                IssueResponse.class
        );

        if (response == null || response.getIssue() == null) {
            throw new MantisException("Impossible de créer le ticket.");
        }

        return response.getIssue();
    }

    @Override
    public void updateIssue(String token, int issueId, Issue.UpdateRequest updateRequest) throws MantisException {
        JsonNode response = getResponse(
                HttpMethod.PATCH,
                newUriBuilder()
                        .path("/api/rest/issues/%s".formatted(issueId))
                        .build(),
                token,
                updateRequest,
                JsonNode.class
        );

        if (response == null) {
            throw new MantisException("Impossible de mettre à jour le ticket.");
        }
    }

    @Override
    public void createIssueNote(String token, int issueId, Note.CreateRequest createRequest) throws MantisException {
        JsonNode response = getResponse(
                HttpMethod.POST,
                newUriBuilder()
                        .path("/api/rest/issues/%s/notes".formatted(issueId))
                        .build(),
                token,
                createRequest,
                JsonNode.class
        );

        if (response == null) {
            throw new MantisException("Impossible de créer le commentaire.");
        }
    }

    @Override
    public void createAttachment(String token, int issueId, Attachment.CreateRequest createRequest) throws MantisException {
        JsonNode response = getResponse(
                HttpMethod.POST,
                newUriBuilder()
                        .path("/api/rest/issues/%s/files".formatted(issueId))
                        .build(),
                token,
                createRequest,
                JsonNode.class
        );

        if (response == null) {
            throw new MantisException("Impossible de créer la pièce jointe.");
        }
    }

    @Override
    public List<Filter> getFilters(String token) throws InvalidMantisSessionException, MantisException {
        FiltersResponse response = getResponse(
                HttpMethod.GET,
                newUriBuilder().path("/api/rest/filters").build(),
                token,
                null,
                FiltersResponse.class
        );

        if (response == null) {
            throw InvalidMantisSessionException.expired();
        }

        return response.getFilters();
    }

    @Override
    public void forgotPassword(String username, String email) throws MantisException {
        try {
            Connection.Response response = Jsoup.connect(baseUrl + "/lost_pwd_page.php").execute();

            MultiValueMap<String, String> cookiesList = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : response.cookies().entrySet()) {
                cookiesList.put(entry.getKey(), List.of(entry.getValue()));
            }

            Document document = response.parse();

            String lostPwdToken = document.select("input[name=lost_pwd_token]").val();

            webClient
                    .post()
                    .uri(uriBuilder -> uriBuilder
                            .path("/lost_pwd.php")
                            .build())
                    .cookies(c -> c.addAll(cookiesList))
                    .headers(formContentType)
                    .header("Referer", baseUrl + "/lost_pwd_page.php?username=%s".formatted(username))
                    .bodyValue("lost_pwd_token=" + lostPwdToken + "&username=" + username + "&email=" + email)
                    .retrieve()
                    .bodyToMono(String.class).block();
        } catch (IOException e) {
            throw new MantisException("Echec de lancement de la procédure de réinitialisation du mot de passe.");
        }
    }

    @Override
    public void resetPassword(int userId, String token, String password) throws MantisException {
        try {
            Connection.Response response =
                    Jsoup.connect(baseUrl + "/verify.php?id=%s&confirm_hash=%s".formatted(userId, token)).execute();

            MultiValueMap<String, String> cookiesList = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : response.cookies().entrySet()) {
                cookiesList.put(entry.getKey(), List.of(entry.getValue()));
            }

            Document document = response.parse();

            String accountUpdateToken = document.select("input[name=account_update_token]").val();
            String realName = document.select("input[name=realname]").val();

            if (accountUpdateToken.isEmpty() || realName.isEmpty()) {
                throw new MantisException("Le Jeton de réinitialisation n'est pas valide.");
            }

            webClient
                    .post()
                    .uri(uriBuilder -> uriBuilder
                            .path("/account_update.php")
                            .build())
                    .cookies(c -> c.addAll(cookiesList))
                    .headers(formContentType)
                    .header("Referer", baseUrl + "/verify.php?id=%s&confirm_hash=%s".formatted(userId, token))
                    .bodyValue("verify_user_id=%s&account_update_token=%s&realname=%s&password=%s&password_confirm=%s".formatted(
                            userId, accountUpdateToken, realName, password, password
                    ))
                    .retrieve()
                    .bodyToMono(String.class).block();
        } catch (IOException e) {
            throw new MantisException("Echec de la réinitialisation du mot de passe.");
        }
    }

    @Override
    public List<Project> getProjects(String token) throws InvalidMantisSessionException, MantisException {
        ProjectsResponse response = getResponse(
                HttpMethod.GET,
                newUriBuilder().path("/api/rest/projects").build(),
                token,
                null,
                ProjectsResponse.class
        );

        if (response == null) {
            throw InvalidMantisSessionException.expired();
        }

        return response.getProjects();
    }

    @Override
    public CountResponse getCount(String token, IssuesFilter filter) throws InvalidMantisSessionException, MantisException{
        UriBuilder uriBuilder = newUriBuilder().path("/api/rest/custom/issues/count");
		
		if (filter.hasProjectId()) {
			uriBuilder.queryParam("project_id", filter.getProjectId());
		}

		if (filter.hasFilterId()) {
			uriBuilder.queryParam("filter_id", filter.getFilterId());
		}

		CountResponse response = getResponse(
                HttpMethod.GET,
                uriBuilder.build(),
                token,
                null,
                CountResponse.class
        );

        if (response == null) {
            throw InvalidMantisSessionException.expired();
        }

        return response;
    }
}
