package fr.itroom.mantis.infrastructure.client;

import fr.itroom.mantis.domain.mantis.filter.Filter;
import fr.itroom.mantis.domain.mantis.issue.Issue;
import fr.itroom.mantis.domain.mantis.issue.attachment.Attachment;
import fr.itroom.mantis.domain.mantis.issue.note.Note;
import fr.itroom.mantis.domain.mantis.project.Project;
import fr.itroom.mantis.domain.mantis.user.UserBase;
import fr.itroom.mantis.domain.web.request.IssuesFilter;
import fr.itroom.mantis.infrastructure.exception.EntityNotFoundException;
import fr.itroom.mantis.infrastructure.exception.InvalidMantisSessionException;
import fr.itroom.mantis.infrastructure.exception.MantisException;
import org.springframework.http.ResponseCookie;
import fr.itroom.mantis.domain.mantis.response.CountResponse;
import org.springframework.security.authentication.BadCredentialsException;

import java.util.List;

public interface IMantisApiClient {

    /**
     * Récupérer une session sur Mantis
     *
     * @param token contenu du cookie de session
     * @return l'utilisateur de la session
     * @throws InvalidMantisSessionException si la session n'existe pas
     */
    UserBase getSession(String token) throws InvalidMantisSessionException;

    /**
     * Créer une session sur Mantis
     * Attention : ne vérifie pas la validité des identifiants, il faut vérifier que la session est valide en appelant {@link #getSession(String)}
     *
     * @param username
     * @param password
     * @return les cookies de session
     * @throws BadCredentialsException si une erreur survient lors de la création de la session
     */
    ResponseCookie createSession(String username, String password) throws BadCredentialsException;

    /**
     * Supprimer une session sur Mantis
     *
     * @param token contenu du cookie de session
     */
    void deleteSession(String token);

    /**
     * Récupérer les tickets de l'utilisateur
     *
     * @param token  contenu du cookie de session
     * @param filter filtre
     * @return les tickets visibles par l'utilisateur
     */
    List<Issue> getIssues(String token, IssuesFilter filter) throws InvalidMantisSessionException, MantisException;

    /**
     * Récupérer les détails d'un ticket
     *
     * @param token   contenu du cookie de session
     * @param issueId id du ticket
     * @return les détails du ticket
     * @throws InvalidMantisSessionException si la session n'existe pas
     * @throws EntityNotFoundException       si le ticket n'existe pas
     */
    Issue getIssue(String token, int issueId) throws InvalidMantisSessionException, EntityNotFoundException, MantisException;

    /**
        * Récupérer le compteur de tickets

        * @param token contenu du cookie de session
        * @param filter filtre
     */
    CountResponse getCount(String token, IssuesFilter filter) throws InvalidMantisSessionException, MantisException;

    /**
     * Récupérer une pièce jointe d'un ticket
     *
     * @param token        contenu du cookie de session
     * @param issueId      id du ticket
     * @param attachmentId id de la pièce jointe
     * @return la pièce jointe
     * @throws InvalidMantisSessionException si la session n'existe pas
     * @throws EntityNotFoundException       si le ticket ou la pièce jointe n'existe pas
     */
    Attachment getAttachment(String token, int issueId, int attachmentId) throws InvalidMantisSessionException, EntityNotFoundException, MantisException;

    /**
     * Créer un nouveau ticket
     *
     * @param token         contenu du cookie de session
     * @param createRequest informations du ticket
     * @return le ticket créé
     * @throws InvalidMantisSessionException si la session n'existe pas
     */
    Issue createIssue(String token, Issue.CreateRequest createRequest) throws InvalidMantisSessionException, MantisException;

    /**
     * Mettre à jour un ticket
     *
     * @param token         contenu du cookie de session
     * @param issueId       id du ticket
     * @param updateRequest informations du ticket
     */
    void updateIssue(String token, int issueId, Issue.UpdateRequest updateRequest) throws MantisException;

    /**
     * Créer une note sur un ticket (avec ou sans pièce jointe)
     *
     * @param token         contenu du cookie de session
     * @param issueId       id du ticket
     * @param createRequest informations de la note
     * @throws InvalidMantisSessionException si la session n'existe pas
     * @throws MantisException               si une erreur survient lors de la création de la note
     */
    void createIssueNote(String token, int issueId, Note.CreateRequest createRequest) throws InvalidMantisSessionException,
            MantisException;

    /**
     * Créer une pièce jointe sur un ticket
     *
     * @param token         contenu du cookie de session
     * @param issueId       id du ticket
     * @param createRequest informations de(s) pièce(s) jointe(s)
     * @throws InvalidMantisSessionException si la session n'existe pas
     * @throws MantisException               si une erreur survient lors de la création de la pièce jointe
     */
    void createAttachment(String token, int issueId, Attachment.CreateRequest createRequest) throws InvalidMantisSessionException, MantisException;

    /**
     * Récupérer les filtres publics et API accessibles pour l'utilisateur
     *
     * @param token contenu du cookie de session
     * @return les filtres
     * @throws InvalidMantisSessionException si la session n'existe pas
     */
    List<Filter> getFilters(String token) throws InvalidMantisSessionException, MantisException;

    /**
     * Initialiser le processus de réinitialisation du mot de passe
     *
     * @param username nom d'utilisateur
     * @param email    email de l'utilisateur
     */
    void forgotPassword(String username, String email) throws MantisException;

    /**
     * Réinitialiser le mot de passe
     *
     * @param userId   id de l'utilisateur
     * @param token    token de réinitialisation
     * @param password nouveau mot de passe
     */
    void resetPassword(int userId, String token, String password) throws MantisException;

    /**
     * Récupérer les projets et sous-projets accessibles pour l'utilisateur
     *
     * @param token contenu du cookie de session
     * @return les projets
     * @throws InvalidMantisSessionException si la session n'existe pas
     * @throws MantisException               si une erreur survient lors de la récupération des projets
     */
    List<Project> getProjects(String token) throws InvalidMantisSessionException, MantisException;
}
