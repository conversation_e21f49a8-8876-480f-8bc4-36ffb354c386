package fr.itroom.mantis.domain.web.request;

import jakarta.validation.constraints.PositiveOrZero;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class IssuesFilter extends Pageable {

    @PositiveOrZero
    private int projectId;

    private boolean selfAssignedOnly;

    @PositiveOrZero
    private int filterId;

    private String sort;
    
    private String summary;

    public boolean hasProjectId() {
        return projectId > 0;
    }

    public boolean hasFilterId() {
        return filterId > 0;
    }

    public boolean hasSort() {
        return sort != null && !sort.isEmpty();
    }

    public boolean hasSummary() {
        return summary != null && !summary.isEmpty();
    }

}
