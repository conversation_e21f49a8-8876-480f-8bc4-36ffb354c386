package fr.itroom.mantis.service;

import fr.itroom.mantis.domain.auth.model.UserAuthentication;
import fr.itroom.mantis.domain.mantis.issue.Issue;
import fr.itroom.mantis.domain.mantis.issue.attachment.Attachment;
import fr.itroom.mantis.domain.mantis.issue.attribute.Status;
import fr.itroom.mantis.domain.mantis.issue.attribute.ViewState;
import fr.itroom.mantis.domain.mantis.issue.history.History;
import fr.itroom.mantis.domain.mantis.issue.history.HistoryType;
import fr.itroom.mantis.domain.mantis.issue.history.types.FieldUpdatedHistory;
import fr.itroom.mantis.domain.mantis.issue.history.types.RelationshipHistory;
import fr.itroom.mantis.domain.mantis.issue.note.Note;
import fr.itroom.mantis.domain.mantis.issue.note.NoteType;
import fr.itroom.mantis.domain.mantis.project.ProjectBase;
import fr.itroom.mantis.domain.mantis.user.MantisSessionUser;
import fr.itroom.mantis.domain.mantis.user.UserBase;
import fr.itroom.mantis.domain.web.request.IssueCreateRequest;
import fr.itroom.mantis.domain.web.request.IssueUpdateRequest;
import fr.itroom.mantis.domain.web.request.IssuesFilter;
import fr.itroom.mantis.domain.web.response.issue.CountResponse;
import fr.itroom.mantis.domain.web.response.issue.IssueDetailsResponse;
import fr.itroom.mantis.domain.web.response.issue.IssueResponse;
import fr.itroom.mantis.domain.web.response.issue.event.*;
import fr.itroom.mantis.infrastructure.client.IMantisApiClient;
import fr.itroom.mantis.infrastructure.exception.EntityNotFoundException;
import fr.itroom.mantis.infrastructure.exception.InvalidMantisSessionException;
import fr.itroom.mantis.infrastructure.exception.MantisException;
import fr.itroom.mantis.service.interfaces.IFilterService;
import fr.itroom.mantis.service.interfaces.IIssueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ContentDisposition;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;

@Service
public class IssueService implements IIssueService {

    private final IMantisApiClient mantisApiClient;
    private final IFilterService filterService;

    @Autowired
    public IssueService(IMantisApiClient mantisApiClient, IFilterService filterService) {
        this.mantisApiClient = mantisApiClient;
        this.filterService = filterService;
    }

    @Override
    public List<IssueResponse> getIssues(UserAuthentication authentication, IssuesFilter filter) throws InvalidMantisSessionException, MantisException {
        if (!filter.hasFilterId()) {
            if (!filter.isSelfAssignedOnly()) {
                filter.setFilterId(filterService.getDefaultFilterID(authentication));
            } else {
                filter.setFilterId(filterService.getSelfAssignedFilterID(authentication));
            }
        }

        List<Issue> issues = mantisApiClient.getIssues(authentication.getMantisSessionToken(), filter);

        return issues.stream().map(IssueResponse::new).toList();
    }

    @Override
    public IssueDetailsResponse getIssueDetails(UserAuthentication authentication, int issueId) throws InvalidMantisSessionException, EntityNotFoundException, MantisException {
        Issue issue = mantisApiClient.getIssue(authentication.getMantisSessionToken(), issueId);

        List<AttachmentEvent> attachments = new ArrayList<>();
        ArrayList<EventStack> events = new ArrayList<>();

        if (issue.hasAttachments()) {
            appendAttachments(events, attachments, issue);
        }

        if (issue.hasNotes()) {
            for (Note note : issue.getNotes()) {
                if (note.getType() == NoteType.UNKNOWN) {
                    continue;
                }

                appendToStack(events, new NoteEvent(note));
            }
        }

        if (issue.hasHistory()) {
            appendHistory(events, issue.getHistory());
        }

        for (EventStack eventStack : events) {
            eventStack.sort();
        }

        events.sort(Comparator.comparing(EventStack::getCreatedAt));

        return new IssueDetailsResponse(issue, attachments, events,
                issue.allowHandlerUpdate(authentication.getPrincipal()));
    }

    @Override
    public ResponseEntity<byte[]> getAttachment(UserAuthentication authentication, int issueId, int attachmentId) throws InvalidMantisSessionException, EntityNotFoundException, MantisException {
        Attachment attachment = mantisApiClient.getAttachment(authentication.getMantisSessionToken(), issueId,
                attachmentId);

        byte[] data = Base64.getDecoder().decode(attachment.getContent());

        String type = attachment.getContentType().split(";")[0];

        String mode = "attachment";

        if (type.contains("pdf")) {
            mode = "inline";
        }

        ContentDisposition contentDisposition = ContentDisposition.builder(mode)
                .filename(attachment.getFilename(), StandardCharsets.UTF_8)
                .build();

        return ResponseEntity.ok()
                .header("Content-Type", type)
                .header("Content-Disposition", contentDisposition.toString())
                .body(data);
    }

    @Override
    public IssueResponse createIssue(UserAuthentication authentication, IssueCreateRequest request) throws EntityNotFoundException, IOException, InvalidMantisSessionException, MantisException {
        MantisSessionUser user = authentication.getPrincipal();

        ProjectBase projectBase = user.getProject(request.getProjectId());

        if (projectBase == null) {
            throw EntityNotFoundException.project();
        }

        Issue.CreateRequest createRequest = new Issue.CreateRequest();
        createRequest.setProject(projectBase);
        createRequest.setSummary(request.getSummary());
        createRequest.setDescription(request.getDescription());
        createRequest.setPriority(request.getPriority());
        createRequest.setSeverity(request.getSeverity());

        if (request.getFiles() != null) {
            for (MultipartFile file : request.getFiles()) {
                createRequest.appendFile(file);
            }
        }

        Issue issue = mantisApiClient.createIssue(authentication.getMantisSessionToken(), createRequest);

        return new IssueResponse(issue);
    }

    @Override
    public void updateIssue(UserAuthentication authentication, int issueId, IssueUpdateRequest request) throws InvalidMantisSessionException, MantisException, IOException, EntityNotFoundException {
        Issue issue = mantisApiClient.getIssue(authentication.getMantisSessionToken(), issueId);
        Issue.UpdateRequest updateRequest = new Issue.UpdateRequest();

        updateStatus(updateRequest, issue, request.getUpdatedStatus());

        if (issue.getHandler() != null && !issue.getHandler().isItRoomUser()) {
            Status newStatus = request.getUpdatedStatus();

            if (newStatus != Status.CLOSED) {
                UserBase handler = issue.findNextHandler();
                updateRequest.setHandler(handler);
            }
        }

        if (updateRequest.hasChanges()) {
            mantisApiClient.updateIssue(authentication.getMantisSessionToken(), issueId, updateRequest);
        }

        try {
            createCommentAndAttachment(authentication, issueId, request);
        } catch (IOException e) {
            throw new MantisException("Error while reading files");
        }
    }

    /**
     * Met à jour le statut d'un ticket si le nouveau statut est autorisé et différent du statut actuel
     *
     * @param updateRequest requête de mise à jour en construction
     * @param issue         ticket à mettre à jour
     * @param updatedStatus nouveau statut
     * @return true si le statut a été modifié
     */
    private boolean updateStatus(Issue.UpdateRequest updateRequest, Issue issue, Status updatedStatus) {
        Status[] allowedStatus = issue.getStatus().getAllowedStatus();
        boolean found = false;

        for (Status status : allowedStatus) {
            if (status == updatedStatus) {
                found = true;
                break;
            }
        }

        if (!found || updatedStatus == issue.getStatus()) {
            return false;
        }

        updateRequest.setStatus(updatedStatus);
        return true;
    }

    /**
     * Crée un commentaire ou une pièce jointe sur un ticket
     *
     * @param issueId        id du ticket
     * @param authentication session courante
     * @param request        informations à ajouter
     * @throws InvalidMantisSessionException si la session n'existe pas
     * @throws MantisException               si la mise à jour du ticket a échoué
     * @throws IOException                   si une erreur survient lors de la lecture des fichiers
     */
    private void createCommentAndAttachment(UserAuthentication authentication,
                                            int issueId,
                                            IssueUpdateRequest request
    ) throws InvalidMantisSessionException, MantisException, IOException {
        if (request.hasMessage()) {
            Note.CreateRequest createRequest = new Note.CreateRequest();
            createRequest.setText(request.getMessage().trim());
            createRequest.setViewState(ViewState.PUBLIC);

            if (request.hasFiles()) {
                for (MultipartFile file : request.getFiles()) {
                    createRequest.appendFile(file);
                }
            }

            mantisApiClient.createIssueNote(authentication.getMantisSessionToken(), issueId, createRequest);
        } else if (request.hasFiles()) {
            Attachment.CreateRequest createRequest = new Attachment.CreateRequest();

            for (MultipartFile file : request.getFiles()) {
                createRequest.appendFile(file);
            }

            mantisApiClient.createAttachment(authentication.getMantisSessionToken(), issueId, createRequest);
        }
    }

    /**
     * Ajoute un événement à la liste des événements correspondante
     *
     * @param list
     * @param event
     */
    private void appendToStack(List<EventStack> list, Event event) {
        EventStack stack = null;

        for (EventStack eventStack : list) {
            if (eventStack.isStackable(event)) {
                stack = eventStack;
            }
        }

        if (stack == null) {
            stack = new EventStack(event.getCreatedAt(), event.getUser());
            list.add(stack);
        }

        stack.appendElement(event);
    }

    /**
     * Ajoute les historiques à la liste des événements
     *
     * @param events  liste des événements à remplir
     * @param history historiques
     */
    private void appendHistory(List<EventStack> events, List<History> history) {
        for (History historyItem : history) {
            if (historyItem.getType() == HistoryType.UNKNOWN) {
                continue;
            }

            if (historyItem instanceof FieldUpdatedHistory<?> fieldUpdatedHistory) {
                appendToStack(events, new FieldHistoryEvent<>(fieldUpdatedHistory));
            } else if (historyItem.getType() == HistoryType.RELATIONSHIP_ADDED || historyItem.getType() == HistoryType.RELATIONSHIP_DELETED) {
                appendToStack(events, new RelationshipHistoryEvent((RelationshipHistory) historyItem));
            }
        }
    }

    /**
     * Ajoute les pièces jointes à une des listes suivantes :
     * - Attachments : liste des pièces jointes initiales, si créé dans les 5 secondes suivant la création du ticket
     * - Events : liste des événements, le cas échéant
     * <p>
     * Le délai de 5 secondes est arbitraire, il permet de compenser le temps de traitement des pièces jointes lors
     * de leur création
     *
     * @param events      liste des événements à remplir
     * @param attachments pièces jointes initiales de l'issue à remplir
     * @param issue       issue à traiter
     */
    private void appendAttachments(List<EventStack> events, List<AttachmentEvent> attachments, Issue issue) {
        for (Attachment attachment : issue.getAttachments()) {
            if (attachment.getCreatedAt().minusSeconds(5).isBefore(issue.getCreatedAt())) {
                attachments.add(new AttachmentEvent(attachment));
            } else {
                appendToStack(events, new AttachmentEvent(attachment));
            }
        }
    }

    @Override
    public CountResponse getCount(UserAuthentication authentication, IssuesFilter filter) throws InvalidMantisSessionException, MantisException {
		if (!filter.hasFilterId()) {
            if (!filter.isSelfAssignedOnly()) {
                filter.setFilterId(filterService.getDefaultFilterID(authentication));
            } else {
                filter.setFilterId(filterService.getSelfAssignedFilterID(authentication));
            }
        }

        fr.itroom.mantis.domain.mantis.response.CountResponse response = mantisApiClient.getCount(authentication.getMantisSessionToken(), filter);

        return new CountResponse(response.getCount(), response.getPageCount());
    }
}
