package fr.itroom.mantis.service.interfaces;

import fr.itroom.mantis.domain.auth.model.UserAuthentication;
import fr.itroom.mantis.domain.web.request.IssueCreateRequest;
import fr.itroom.mantis.domain.web.request.IssueUpdateRequest;
import fr.itroom.mantis.domain.web.request.IssuesFilter;
import fr.itroom.mantis.domain.web.response.issue.CountResponse;
import fr.itroom.mantis.domain.web.response.issue.IssueDetailsResponse;
import fr.itroom.mantis.domain.web.response.issue.IssueResponse;
import fr.itroom.mantis.infrastructure.exception.EntityNotFoundException;
import fr.itroom.mantis.infrastructure.exception.InvalidMantisSessionException;
import fr.itroom.mantis.infrastructure.exception.MantisException;
import org.springframework.http.ResponseEntity;



import java.io.IOException;
import java.util.List;

public interface IIssueService {

    /**
     * Récupèrer les tickets de l'utilisateur
     *
     * @param authentication session courante
     * @param filter         filtre
     * @return les tickets visibles par l'utilisateur
     */
    List<IssueResponse> getIssues(UserAuthentication authentication, IssuesFilter filter) throws InvalidMantisSessionException, MantisException;

    /**
     * Récupèrer les détails d'un ticket
     *
     * @param authentication session courante
     * @param issueId        id du ticket
     * @return les détails du ticket
     */
    IssueDetailsResponse getIssueDetails(UserAuthentication authentication, int issueId) throws InvalidMantisSessionException, EntityNotFoundException, MantisException;

    /**
     * Récupèrer le détail d'une pièce jointe d'un ticket
     *
     * @param authentication session courante
     * @param issueId        id du ticket
     * @return le contenu de la pièce jointe
     */
    ResponseEntity<byte[]> getAttachment(UserAuthentication authentication, int issueId,
                                         int attachmentId) throws InvalidMantisSessionException, EntityNotFoundException, MantisException;

    /**
     * Créer un ticket
     *
     * @param authentication session courante
     * @param request        les informations du ticket
     * @return le ticket créé
     */
    IssueResponse createIssue(UserAuthentication authentication, IssueCreateRequest request) throws EntityNotFoundException, IOException, InvalidMantisSessionException, MantisException;

    /**
     * Mettre à jour un ticket
     *
     * @param authentication session courante
     * @param issueId        id du ticket
     * @param request        les informations à mettre à jour
     */
    void updateIssue(UserAuthentication authentication, int issueId, IssueUpdateRequest request) throws InvalidMantisSessionException, MantisException, IOException, EntityNotFoundException;

    /**
     * Récupèrer le nombre de tickets
     *
     * @param authentication session courante
     * @param filter         filtre
     * @return le nombre de tickets
     */
    CountResponse getCount(UserAuthentication authentication, IssuesFilter filter) throws InvalidMantisSessionException, MantisException;

}
