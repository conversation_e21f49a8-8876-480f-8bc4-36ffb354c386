package fr.itroom.mantis.controller;

import fr.itroom.mantis.domain.auth.model.UserAuthentication;
import fr.itroom.mantis.domain.web.request.IssueCreateRequest;
import fr.itroom.mantis.domain.web.request.IssueUpdateRequest;
import fr.itroom.mantis.domain.web.request.IssuesFilter;
import fr.itroom.mantis.domain.web.response.issue.CountResponse;
import fr.itroom.mantis.domain.web.response.issue.IssueDetailsResponse;
import fr.itroom.mantis.domain.web.response.issue.IssueResponse;
import fr.itroom.mantis.infrastructure.exception.EntityNotFoundException;
import fr.itroom.mantis.infrastructure.exception.InvalidMantisSessionException;
import fr.itroom.mantis.infrastructure.exception.MantisException;
import fr.itroom.mantis.service.interfaces.IIssueService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/v1/issues")
public class IssueController {

    private final IIssueService issueService;

    @Autowired
    public IssueController(IIssueService issueService) {
        this.issueService = issueService;
    }

    @GetMapping
    public List<IssueResponse> getIssues(UserAuthentication userAuthentication,
                                         @Valid IssuesFilter filter) throws InvalidMantisSessionException, MantisException {
        return issueService.getIssues(userAuthentication, filter);
    }

    @PostMapping
    public IssueResponse createIssue(UserAuthentication userAuthentication,
                                     @Valid @ModelAttribute IssueCreateRequest request) throws InvalidMantisSessionException, EntityNotFoundException, IOException, MantisException {
        return issueService.createIssue(userAuthentication, request);
    }

	@GetMapping("/count")
    public CountResponse getCount(UserAuthentication userAuthentication,
                                         IssuesFilter filter) throws InvalidMantisSessionException, MantisException {
        return issueService.getCount(userAuthentication, filter);
    }

    @PostMapping("/{issueId}")
    public void updateIssue(UserAuthentication userAuthentication, @PathVariable int issueId,
                            @Valid @ModelAttribute IssueUpdateRequest request) throws InvalidMantisSessionException,
            EntityNotFoundException, IOException, MantisException {
        issueService.updateIssue(userAuthentication, issueId, request);
    }

    @GetMapping("/{issueId:[0-9]+}")
    public IssueDetailsResponse getIssue(UserAuthentication userAuthentication, @PathVariable int issueId) throws InvalidMantisSessionException,
            EntityNotFoundException, MantisException {
        return issueService.getIssueDetails(userAuthentication, issueId);
    }
  

    @GetMapping("/{issueId}/files/{attachmentId}")
    public ResponseEntity<byte[]> getAttachment(UserAuthentication userAuthentication, @PathVariable int issueId,
                                                @PathVariable int attachmentId) throws InvalidMantisSessionException,
            EntityNotFoundException, MantisException {
        return issueService.getAttachment(userAuthentication, issueId, attachmentId);
    }
}
