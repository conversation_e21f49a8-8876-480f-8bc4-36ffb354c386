# mantis-client-backend

[![Build and Test](https://github.com/it-room/mantis-client-backend/actions/workflows/build-and-test.yaml/badge.svg?branch=develop)](https://github.com/it-room/mantis-client-backend/actions/workflows/build-and-test.yaml)
[![Quality Gate Status](https://sonarqube.tech.itroom.fr/api/project_badges/measure?project=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90&metric=alert_status&token=sqb_b64dc7bc4ef9f2bea0a501157e0750ff085f2f6a)](https://sonarqube.tech.itroom.fr/dashboard?id=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90)
[![Maintainability Rating](https://sonarqube.tech.itroom.fr/api/project_badges/measure?project=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90&metric=sqale_rating&token=sqb_b64dc7bc4ef9f2bea0a501157e0750ff085f2f6a)](https://sonarqube.tech.itroom.fr/dashboard?id=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90)
[![Reliability Rating](https://sonarqube.tech.itroom.fr/api/project_badges/measure?project=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90&metric=reliability_rating&token=sqb_b64dc7bc4ef9f2bea0a501157e0750ff085f2f6a)](https://sonarqube.tech.itroom.fr/dashboard?id=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90)
[![Security Rating](https://sonarqube.tech.itroom.fr/api/project_badges/measure?project=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90&metric=security_rating&token=sqb_b64dc7bc4ef9f2bea0a501157e0750ff085f2f6a)](https://sonarqube.tech.itroom.fr/dashboard?id=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90)
[![Coverage](https://sonarqube.tech.itroom.fr/api/project_badges/measure?project=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90&metric=coverage&token=sqb_b64dc7bc4ef9f2bea0a501157e0750ff085f2f6a)](https://sonarqube.tech.itroom.fr/dashboard?id=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90)

Ce repository contient les sources du serveur pour l'API Backend de l'application Mantis Client.

## Présentation

L'application est développé en Java 21 avec le framework Spring Boot.

L'application se base sur l'API de MantisBT.

Documentation API MantisBT : [https://documenter.getpostman.com/view/29959/mantis-bug-tracker-rest-api/7Lt6zkP#intro](https://documenter.getpostman.com/view/29959/mantis-bug-tracker-rest-api/7Lt6zkP#intro)

L'authentification se fait via Mantis. Se référer à la dernière section pour les contraintes techniques liées à Mantis.

Des tests unitaires et d'intégration sont présents pour garantir le bon fonctionnement de l'application.

Les tests sont lancés automatiquement à l'aide d'une CI configurée sur Github.

## Mise en place de l'environnement de développement

### 1. Prérequis

- Java 21 [https://adoptium.net/fr/temurin/releases/?version=21](https://adoptium.net/fr/temurin/releases/?version=21)
- Maven
- IDE Java : Intellij IDEA
- Un serveur MantisBT en version 2.21.1 minimum

### 2. Configuration de l'application

Le fichier de configuration de l'application `src/main/ressources/application.yml` est déjà configuré.

Certaines sections sont écrasables avec des variables d'environnement.

Pour l'environnement de développement seules les variables suivantes sont à définir :

- MANTIS_HOST : Nom d'hôte du Mantis à utiliser
- MANTIS_PROTOCOL : Protocole à utiliser http ou https

Il est possible de les définir dans les configurations de run d'Intellij IDEA.

### 3. Procédures

#### 3.1 Lancement de l'application

Il est maintenant possible de lancer l'application à l'aide de la configuration par défaut d'Intellij IDEA.

Il sera alors possible d'accéder à :

- API: http://localhost:8080/api/v1

#### 3.2 Lancement des tests

Il est recommandé de lancer les tests localement avant de pousser ses changements.

Cela peut se faire avec Intellij IDEA, en utilisant "Run all tests", le debugage sera alors facilité.

Sinon, il est possible de les lancer avec Maven :

```shell
mvn clean verify
```

#### 3.3 Intégration avec Sonar

L'application est connecté à Sonar qui permet l'analyse de la qualité du code (couverture, mauvaise pratique).

Une CI est configurée pour lancer automatiquement les tests et un rapport Sonar lors d'un commit lié à une PR dont
la branche de destination est `develop`.

[Lien vers le projet Sonar](https://sonarqube.tech.itroom.fr/dashboard?id=it-room_mantis-client-backend_737da8a0-05e8-4446-bd14-9a62535c8c90)

## Mise en place de l'environnement de production

### 1. Prérequis

- Docker, docker-compose
- Make
- Un serveur MantisBT en version 2.21.1 minimum
- Reverse proxy Traefik

### 2. Cloner le repository pour la première fois

Le contenu de cette section n'est à appliquer que lors de la configuration initiale, se base sur le Wiki (Section 
Configuration de Git): [Création d'une recette sur un VPS OVH](https://wiki.itroom.fr/index.php/Cr%C3%A9ation_de_recette_ou_de_production)

Génération de la clé de déploiement :
```shell
ssh-keygen -N "" -t ed25519 -f ~/.ssh/github_mantis_client_backend -C "mantis-client-backend@$(hostname)"
```

Celle-ci doit se nommer `github_mantis_client_backend`. Elle doit ensuite être ajoutée comme clé de déploiement sur le 
repository.

Dans le cas de l'utilisation de la même VM pour plusieurs repository, il faut modifier le fichier `~/.ssh/config` pour ajouter :
```bash
Host mantis-client-backend.github.local
  Hostname github.com
  IdentityFile=~/.ssh/github_mantis_client_backend
```

On peut ensuite cloner le repository en utilisant :
```bash
<NAME_EMAIL>:it-room/mantis-client-backend.git
```

Les prochaines fois, un simple `git pull` suffira.

On pourra choisir la release à utiliser en sélectionnant le tag correspondant avec :
```shell
git checkout tags/vX.X.X
```

### 3. Configuration

Créer le fichier `infra/.env` à partir du fichier `infra/.env.dist`

Le modifier avec les informations de la production/recette (mot de passe, clés...)

Liste des variables d'environnement à modifier :

| Nom                 | Description                                          |
|---------------------|------------------------------------------------------|
| DOMAIN_NAME         | Nom de domaine sur lequel doit être accessible l'API |
| MANTIS_HOST         | Nom d'hôte de l'API Mantis                           |
| MANTIS_PROTOCOL     | Protocole à utiliser pour Mantis (https ou http)     |
| CORS_ALLOWED_ORIGIN | URL Racine du frontend (CORS)                        |

_Ces différentes variables d'environnement seront ensuite lues par Spring dans le fichier application.yml_

### 4. Construction de l'image

À chaque modification des sources / changement de version, il faut re-construire les images :

```shell
make build
```

### 5. Démarrage / arrêt de l'application

On utilisera les commandes définies dans le Makefile :

Lancer les services :

```shell
make up
```

Arrêter les services :

```shell
make down
```

## Contraintes techniques liées à Mantis

### Configuration des permissions

L'application se base entièrement sur l'API de Mantis, qui possède ses limites.

Pour commencer MantisBT doit être configuré correctement :

Tous les utilisateurs doivent : 
- avoir accès à l'API REST, à partir du rôle le plus basique (raporteur)
- pouvoir voir l'email et le vrai nom des autres utilisateurs (via les clauses de configuration suivantes : `$g_show_user_email_threshold` et `$g_show_user_realname_threshold`)

L'email est par exemple nécessaire pour le bon fonctionnement de l'assignation automatique lors du changement de 
statut, il est utilisé afin de savoir si un utilisateur est interne à l'entreprise ou externe.

### Configuration de filtres globaux

L'API de Mantis est très pauvre en filtres, elle ne permet d'utiliser que des filtres publics définis par un utilisateur dans Mantis.

Ils doivent être créés par un administrateur et rendu public.

L'application va automatiquement se synchroniser avec les filtres publics toutes les heures.

Les filtres doivent être nommés de la manière suivante :

- **API_DEFAULT** Filtre utilisé par défaut par l'application (lorsque qu'aucun filtre n'est sélectionné)
- **API_SELF_ASSIGNED** Filtre utilisé lorsque là case "assigné à moi" est cochée

Il est possible de créer des filtres supplémentaires, ils doivent être préfixés par **API_FILTER** suivi du nom du filtre :
- **API_FILTER Exemple** Filtre sur le statut, nommé Exemple

Étant donné qu'il n'est pas possible de combiner les filtres, il n'y a qu'une catégorie de filtres supplémentaires, 
qui ne sont affichés que dans un seul sélecteur sur l'interface du frontend.