/*
 Ace Admin Theme v1.4
 Copyright (c) 2016 Mohsen - (twitter.com/responsiweb)

 This program is free software: you can redistribute it and/or modify
 it under the terms of the GNU General Public License as published by
 the Free Software Foundation, either version 3 of the License, or
 (at your option) any later version.

 This program is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License for more details.

 You should have received a copy of the GNU General Public License
 along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
/* skin 3 */
.skin-3 {
    background-color: #BBB;
}
.skin-3 .main-container:before {
    background: #FFF;
    background: -moz-linear-gradient(top, #EEF5FA 0%, #FFF 8%) 0 4px;
    background: -webkit-gradient(linear, 0 0, 0 100%, from(#EEF5FA), color-stop(4%, #FFF)) 0 4px;
    background: -webkit-linear-gradient(top, #EEF5FA 0%, #FFF 8%) 0 4px;
    background: -o-linear-gradient(top, #EEF5FA 0%, #FFF 8%) 0 4px;
    background: -ms-linear-gradient(top, #EEF5FA 0%, #FFF 8%) 0 4px;
    background: linear-gradient(top, #EEF5FA 0%, #FFF 8%) 0 4px;
    -moz-background-size: 100% 26px;
    -webkit-background-size: 100% 26px;
    -o-background-size: 100% 26px;
    -ms-background-size: 100% 26px;
    background-size: 100% 26px;
}
@media (min-width: 768px) {
    .skin-3 .main-container.container:before {
        -webkit-box-shadow: 0 0 0 1px rgba(50, 100, 200, 0.1);
        box-shadow: 0 0 0 1px rgba(50, 100, 200, 0.1);
    }
}
.skin-3 .navbar {
    background: #404040;
}
.skin-3 .sidebar {
    background-color: #DBDBDB;
    border-style: solid;
    border-width: 0 1px 0 0;
    border-color: #A4C6DD;
}
.skin-3 .nav-list > li {
    border-color: #F2F2F2;
}
.skin-3 .nav-list > li > a {
    background-color: #E3E3E3;
    color: #5A5A5A;
}
.skin-3 .nav-list > li > a > .arrow {
    color: inherit;
}
.skin-3 .nav-list > li > a:focus {
    background-color: #e8e8e8;
    color: #5A5A5A;
}
.skin-3 .nav-list > li:hover {
    border-color: #95bad2 !important;
}
.skin-3 .nav-list > li:hover + li {
    border-top-color: #95bad2 !important;
}
.skin-3 .nav-list > li.open {
    border-color: #95bad2 !important;
}
.skin-3 .nav-list > li.open + li {
    border-top-color: #95bad2 !important;
}
.skin-3 .nav-list > li.active {
    border-color: #A4C6DD !important;
}
.skin-3 .nav-list > li.active + li {
    border-color: #A4C6DD !important;
}
.skin-3 .nav-list > li.active + li:last-child {
    border-bottom-color: #F2F2F2 !important;
}
.skin-3 .nav-list > li.active + li:last-child:hover {
    border-bottom-color: #95bad2 !important;
}
.skin-3 .nav-list > li:hover > a {
    background-color: #FFF;
    color: #337dbb;
}
.skin-3 .nav-list > li.open > a,
.skin-3 .nav-list > li.open:hover > a {
    color: #337dbb;
    background-color: #F8F8F8;
}
.skin-3 .nav-list > li.open > a > .arrow {
    color: inherit;
}
.skin-3 .nav-list > li.active > a,
.skin-3 .nav-list > li.active.highlight > a {
    background-color: #f3faff !important;
    color: #4D96CB !important;
}
.skin-3 .nav-list > li:hover:before,
.skin-3 .nav-list > li.open:before {
    display: block;
    background-color: #4f8ab4 !important;
}
.skin-3 .nav-list > li.active:before {
    display: block;
    background-color: #4D96CB !important;
}
.skin-3 .page-content {
    background-color: transparent;
}
.skin-3 .infobox-container .infobox:not(.infobox-dark) {
    border-style: solid;
    background-color: transparent;
}
.skin-3 .nav-list > li.active:after {
    display: none;
}
.skin-3 .nav-list li.active > a:after {
    border-right-color: #FFF;
    border-width: 12px 8px;
    top: 7px;
    right: -1px;
}
.skin-3 .nav-list li.active > a:before {
    display: block;
    content: "";
    position: absolute;
    background-color: transparent;
    border-style: solid;
    border-color: transparent;
    border-right-color: #91bad6;
    right: 0;
    border-width: 12px 8px;
    top: 7px;
}
.skin-3 .nav-list > li.active > .submenu li.active > a:before,
.skin-3 .nav-list > li.active > .submenu li.active > a:after {
    top: 4px;
}
.skin-3 .nav-list li.active.open > a:before {
    display: none;
}
.skin-3 .nav-list li.highlight.active.open > a:before {
    display: block;
}
.skin-3 .nav-list li.active:not(.open) li.active > a:before {
    display: none !important;
}
.skin-3 .nav-list > li.highlight.active > a:after {
    border-left-color: #f3faff;
}
.skin-3 .nav-list > li.highlight.active > a:before {
    border-left-color: #91bad6;
}
.skin-3 .nav-list li > .arrow:before {
    border-right-color: #7fafcf;
}
.skin-3 .nav-list > li .submenu > li.active:not(.open) > a {
    background-color: #F5F7FA;
}
.skin-3 .nav-list > li .submenu > li.active:not(.open) > a:hover {
    background-color: #F1F5F9;
}
@media only screen and (max-width: 991px) {
    .skin-3 .sidebar.responsive .nav-list > li.active.open > a:after {
        display: none;
    }
}
@media only screen and (min-width: 992px) {
    .skin-3 .nav-list li.hover > .submenu {
        border-color: #99bfd9;
    }
}
@media only screen and (min-width: 992px) {
    .skin-3 .nav-list li.hover.active > a:before {
        display: block;
    }
    .skin-3 .nav-list li.hover .submenu > li.active > a:before {
        display: none ;
    }
}
.skin-3 .sidebar.menu-min .nav-list > li > a > .menu-text {
    background-color: #f1f5f9;
    border-color: #A4C6DD;
}
.skin-3 .sidebar.menu-min .nav-list > li.active > a > .menu-text {
    background-color: #EDF3F7;
}
.skin-3 .sidebar.menu-min .nav-list > li > .submenu {
    border-color: #A4C6DD;
    border-top-color: #C9DAE5;
}
.skin-3 .sidebar.menu-min .nav-list > li.active > .arrow:before {
    border-right-color: #709FBF;
}
.skin-3 .sidebar.menu-min .nav-list > li > .arrow:after {
    border-right-color: #EDF3F7;
}
.skin-3 .sidebar.menu-min .nav-list li.active > a:after,
.skin-3 .sidebar.menu-min .nav-list li.active > a:before {
    display: none;
}
.skin-3 .sidebar.menu-min .nav-list > li.active > a:after,
.skin-3 .sidebar.menu-min .nav-list > li.active > a:before {
    display: block;
    border-width: 9px 7px;
    top: 10px;
}
.skin-3 .sidebar.menu-min .nav-list > li.active.highlight > a:after,
.skin-3 .sidebar.menu-min .nav-list > li.active.highlight > a:before {
    border-width: 20px 0 21px 10px;
    top: -1px;
}
.skin-3 .sidebar.menu-min .nav-list > li.active:hover > a:after,
.skin-3 .sidebar.menu-min .nav-list > li.active.hover-show > a:after,
.skin-3 .sidebar.menu-min .nav-list > li.active:hover > a:before,
.skin-3 .sidebar.menu-min .nav-list > li.active.hover-show > a:before {
    display: none;
}
.skin-3 .sidebar.menu-min .sidebar-shortcuts-large {
    background-color: #F5F5F5;
}
@media only screen and (max-width: 991px) {
    .skin-3 .sidebar.menu-min.responsive .nav-list > li.active > a:after,
    .skin-3 .sidebar.menu-min.responsive .nav-list > li.active > a:before {
        display: none;
    }
}
@media only screen and (max-width: 991px) {
    .skin-3 .sidebar.responsive {
        border-bottom-width: 1px !important;
    }
    .skin-3 .sidebar.responsive .nav-list > li.active.open > a:after {
        display: none;
    }
    .skin-3 .sidebar.responsive .nav-list > li.active.highlight > a:after,
    .skin-3 .sidebar.responsive .nav-list > li.active.highlight > a:before {
        display: block;
    }
    .skin-3 .sidebar.navbar-collapse {
        border-bottom-color: #A4C6DD;
    }
    .skin-3 .nav-list li.active > a:after,
    .skin-3 .nav-list li.active > a:before {
        display: none;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li > a > .menu-text {
        background-color: #f1f5f9;
        border-color: #A4C6DD;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li.active > a > .menu-text {
        background-color: #EDF3F7;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li > .submenu {
        border-color: #A4C6DD;
        border-top-color: #C9DAE5;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li.active > .arrow:before {
        border-right-color: #709FBF;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li > .arrow:after {
        border-right-color: #EDF3F7;
    }
    .skin-3 .sidebar.responsive-min .nav-list li.active > a:after,
    .skin-3 .sidebar.responsive-min .nav-list li.active > a:before {
        display: none;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li.active > a:after,
    .skin-3 .sidebar.responsive-min .nav-list > li.active > a:before {
        display: block;
        border-width: 9px 7px;
        top: 10px;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li.active.highlight > a:after,
    .skin-3 .sidebar.responsive-min .nav-list > li.active.highlight > a:before {
        border-width: 20px 0 21px 10px;
        top: -1px;
    }
    .skin-3 .sidebar.responsive-min .nav-list > li.active:hover > a:after,
    .skin-3 .sidebar.responsive-min .nav-list > li.active.hover-show > a:after,
    .skin-3 .sidebar.responsive-min .nav-list > li.active:hover > a:before,
    .skin-3 .sidebar.responsive-min .nav-list > li.active.hover-show > a:before {
        display: none;
    }
    .skin-3 .sidebar.responsive-min .sidebar-shortcuts-large {
        background-color: #F5F5F5;
    }
    .skin-3 .sidebar.responsive-max {
        border-width: 0 1px 1px 0;
    }
    .skin-3 .sidebar.responsive-max .nav-list li.hover.active > a:before {
        display: none;
    }
    .skin-3 .sidebar.responsive-max .nav-list > li.active.open > a:after {
        display: none;
    }
    .skin-3 .sidebar.responsive-max .nav-list > li.active.highlight > a:after,
    .skin-3 .sidebar.responsive-max .nav-list > li.active.highlight > a:before {
        display: block;
    }
    .skin-3 .sidebar.navbar-collapse .sidebar-shortcuts-large {
        background-color: transparent;
    }
}
.skin-3 .sidebar-shortcuts,
.skin-3 .sidebar-shortcuts-mini {
    background-color: #DBDBDB;
}
.skin-3 .sidebar-shortcuts-large > .btn:focus {
    outline: none;
}
.skin-3 .sidebar > .nav-search {
    background-color: #DBDBDB;
}
.skin-3 .sidebar-toggle {
    background-color: #D0D0D0;
    border-color: #F2F2F2;
}
.skin-3 .sidebar-toggle:before {
    border-color: #F2F2F2;
}
.skin-3 .sidebar-toggle > .ace-icon {
    background-color: #FFF;
    border-color: #999;
    color: #999;
}
.skin-3 .sidebar-scroll .nav-wrap-up + .sidebar-toggle:after {
    display: block;
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: -1px;
    border-right: 1px solid #A4C6DD;
}
@media only screen and (max-width: 991px) {
    .skin-3 .sidebar.navbar-collapse .nav-list > li:before {
        height: 43px !important;
    }
    .skin-3 .sidebar.navbar-collapse .sidebar-shortcuts {
        padding: 0 0 3px !important;
    }
}
@media only screen and (min-width: 992px) {
    .skin-3 .nav-list > li.active.hover:hover > a.dropdown-toggle:after,
    .skin-3 .nav-list > li.active.hover.hover-show > a.dropdown-toggle:after,
    .skin-3 .nav-list > li.active.hover:hover > a.dropdown-toggle:before,
    .skin-3 .nav-list > li.active.hover.hover-show > a.dropdown-toggle:before {
        display: none;
    }
}
.skin-3 .main-container .menu-toggler {
    background-color: #62A8D1;
    color: #FFF;
}
.skin-3 .main-container .menu-toggler:before,
.skin-3 .main-container .menu-toggler:after {
    border-color: #FFF;
}
.skin-3 .main-container .menu-toggler > .toggler-text {
    border-top-color: #62A8D1;
}
.skin-3 .main-container .menu-toggler > .toggler-text:after {
    color: #FFF;
}
.skin-3 .navbar .navbar-toggle {
    border-color: rgba(255, 255, 255, 0.15);
    transition: background-color 0.1s ease 0s;
    background-color: #648CAE;
}
.skin-3 .navbar .navbar-toggle:focus {
    background-color: #648CAE;
    border-color: rgba(255, 255, 255, 0.15);
}
.skin-3 .navbar .navbar-toggle:hover {
    background-color: #5782a7;
    border-color: rgba(255, 255, 255, 0.15);
}
.skin-3 .navbar .navbar-toggle.display,
.skin-3 .navbar .navbar-toggle[data-toggle=collapse]:not(.collapsed) {
    background-color: #507899;
    border-color: rgba(255, 255, 255, 0.3);
}
.skin-3 .breadcrumbs {
    border-width: 0;
    background-color: #E7F2F8;
    border-radius: 4px;
    margin: 8px 8px 0;
}
@media only screen and (max-width: 991px) {
    .skin-3 .breadcrumbs {
        margin: 5px 5px 0;
    }
    .skin-3 .menu-toggler + .sidebar.responsive + .main-content .breadcrumbs {
        margin: 0;
        border-radius: 0;
    }
}
@media (min-width: 992px) {
    .skin-3 .breadcrumbs.breadcrumbs-fixed + .page-content {
        padding-top: 57px;
    }
}
@media (min-width: 992px) and (max-width: 991px) {
    .skin-3 .breadcrumbs.breadcrumbs-fixed + .page-content {
        padding-top: 54px;
    }
    .skin-3 .menu-toggler + .sidebar.reponsive + .main-content .breadcrumbs.breadcrumbs-fixed + .page-content {
        padding-top: 49px;
    }
}
@media (min-width: 992px) and (max-width: 991px) {
    .skin-3 .container.main-container .sidebar.compact + .main-content .breadcrumbs-fixed {
        width: 734px;
    }
}
@media (min-width: 992px) and (max-width: 991px) {
    .skin-3 .container.main-container .breadcrumbs-fixed {
        width: 734px;
    }
    .skin-3 .container.main-container .sidebar.menu-min + .main-content .breadcrumbs-fixed {
        width: 734px;
    }
    .skin-3 .container.main-container .sidebar.responsive-min + .main-content .breadcrumbs-fixed {
        width: 691px;
    }
}
@media (min-width: 992px) {
    .skin-3 .container.main-container .sidebar.compact + .main-content .breadcrumbs-fixed {
        width: 843px;
    }
}
@media (min-width: 992px) {
    .skin-3 .container.main-container .breadcrumbs-fixed {
        width: 964px;
    }
    .skin-3 .container.main-container .sidebar + .main-content .breadcrumbs-fixed {
        width: 758px;
    }
    .skin-3 .container.main-container .sidebar.menu-min + .main-content .breadcrumbs-fixed {
        width: 905px;
    }
}
@media (min-width: 1200px) {
    .skin-3 .container.main-container .sidebar.compact + .main-content .breadcrumbs-fixed {
        width: 1043px;
    }
}
@media (min-width: 1200px) {
    .skin-3 .container.main-container .breadcrumbs-fixed {
        width: 1148px;
    }
    .skin-3 .container.main-container .sidebar + .main-content .breadcrumbs-fixed {
        width: 958px;
    }
    .skin-3 .container.main-container .sidebar.menu-min + .main-content .breadcrumbs-fixed {
        width: 1105px;
    }
}
@media only screen and (max-width: 991px) {
    .skin-3 .nav-list li.active > a:before,
    .skin-3 .nav-list li.active > a:after {
        display: none;
    }
}
.skin-3 .sidebar-shortcuts-large > .btn {
    line-height: 26px;
    border-width: 1px;
}
.skin-3 .sidebar-shortcuts-mini {
    padding-top: 3px;
    padding-bottom: 3px;
    padding-left: 1px;
}
.skin-3 .sidebar-shortcuts-mini > .btn {
    border-width: 1px;
    opacity: 1;
    padding: 7px;
    margin: 1px 1px 0 0;
}
@media only screen and (min-width: 992px) {
    .skin-3 .sidebar.h-sidebar {
        background-color: #E3E3E3;
    }
    .skin-3 .sidebar.h-sidebar:before {
        background-color: #CBD0D6;
        border-bottom-width: 0;
    }
    .skin-3 .sidebar.h-sidebar .sidebar-shortcuts-mini > .btn {
        padding: 8px;
    }
    .skin-3 .sidebar.h-sidebar .sidebar-shortcuts-large {
        background-color: #FFF;
        line-height: 36px;
    }
    .skin-3 .sidebar.h-sidebar + .main-content .breadcrumbs {
        border-color: #d5e7f1;
        top: 2px;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li.hover > .submenu {
        border-color: #A4C6DD;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li {
        border-color: #F2F2F2;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li:hover,
    .skin-3 .sidebar.h-sidebar .nav-list > li:hover + li {
        border-left-color: #95bad2;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li:last-child:hover {
        border-right-color: #95bad2;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li.active,
    .skin-3 .sidebar.h-sidebar .nav-list > li.active + li,
    .skin-3 .sidebar.h-sidebar .nav-list > li:hover + li.active {
        border-left-color: #A4C6DD;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li.active:last-child {
        border-right-color: #A4C6DD;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li.active > a:after {
        left: 0;
        top: auto;
        right: auto;
        bottom: -2px;
        left: 50%;
        margin-left: -7px;
        border-color: transparent;
        border-width: 8px 7px !important;
        border-bottom-color: #FFF;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li.active > a:before {
        display: block;
        left: 0;
        top: auto;
        right: auto;
        bottom: -1px;
        left: 50%;
        margin-left: -8px !important;
        border-width: 8px !important;
        border-color: transparent;
        border-bottom-color: #74add7;
    }
    .skin-3 .sidebar.h-sidebar .nav-list > li.hover > .arrow:before {
        border-right-color: transparent;
        border-bottom-color: #7fafcf;
    }
    .skin-3 .sidebar.h-sidebar.menu-min .sidebar-shortcuts {
        max-width: 52px;
        padding-left: 2px;
        padding-right: 2px;
    }
    .skin-3 .sidebar.h-sidebar.menu-min .sidebar-shortcuts-mini > .btn {
        padding: 7px;
    }
    .skin-3 .sidebar.h-sidebar.menu-min .nav-list > li.hover > .submenu {
        border-top-color: #C9DAE5;
    }
    .skin-3 .sidebar.h-sidebar.menu-min .nav-list > li.active > a > .menu-text {
        border-left-color: #A4C6DD;
    }
    .skin-3 .sidebar.h-sidebar.menu-min .nav-list > li > .arrow:after {
        border-bottom-color: #EDF3F7;
    }
}
.skin-3 .sidebar-scroll .sidebar-shortcuts {
    border-bottom-color: 1px solid #99B6C9;
}
.skin-3 .sidebar-scroll .sidebar-toggle {
    border-top-color: #99B6C9;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .skin-3 .navbar.navbar-collapse {
        background-color: transparent;
    }
    .skin-3 .navbar.navbar-collapse:before,
    .skin-3 .navbar.navbar-collapse .navbar-container {
        background: #404040;
    }
}
.skin-3 .nav-list > li.disabled:before {
    display: none !important;
}