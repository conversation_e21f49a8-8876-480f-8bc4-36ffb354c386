log_format apm '"$time_local" client=$remote_addr '
               'method=$request_method request="$request" '
               'request_length=$request_length '
               'status=$status bytes_sent=$bytes_sent '
               'body_bytes_sent=$body_bytes_sent '
               'referer=$http_referer '
               'user_agent="$http_user_agent" '
               'upstream_addr=$upstream_addr '
               'upstream_status=$upstream_status '
               'request_time=$request_time '
               'upstream_response_time=$upstream_response_time '
               'upstream_connect_time=$upstream_connect_time '
               'upstream_header_time=$upstream_header_time';

server {
    server_name ~.*;
    server_tokens off;
                                                   
    index index.php;                  

    location ~ \.(sql|tar|gz|tar.gz|tgz)$ { deny all; }

    location / {
        root /app;
        try_files $uri /index.php$is_args$args;
    }

    location ~ \.php$ {       
        root /app;                                                                                                                                   
        fastcgi_split_path_info ^(.+\.php)(/.+)$;                                                  
        include fastcgi_params;                                                                    
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;                          
        fastcgi_param SCRIPT_NAME $fastcgi_script_name;                                            
        fastcgi_index index.php;                           
        fastcgi_pass php:9000;
   }
   
   location ^~ /api/rest {                                                                             
        alias /app/api/rest;                                                       
        index index.php;                                                                           
                                                                                                   
        if (!-e $request_filename) { rewrite ^ /api/rest/index.php last; }                              
                                                                                                   
        location ~ \.php$ {                                                                        
            if (!-f $request_filename) { return 404; }                                             
            fastcgi_pass php:9000;
                                                                                                   
            include fastcgi_params;                                                                
            fastcgi_param SCRIPT_FILENAME $request_filename;                                       
            fastcgi_param SCRIPT_NAME $fastcgi_script_name;                                        
        }                                                                                          
    }       

    error_log /dev/stderr debug;
    access_log /dev/stdout apm;
}
