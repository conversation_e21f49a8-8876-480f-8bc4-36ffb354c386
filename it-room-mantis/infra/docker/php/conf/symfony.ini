[opcache]
opcache.preload_user=www-data
opcache.preload=/app/config/preload.php
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
opcache.validate_timestamps=1
realpath_cache_size=4096K
realpath_cache_ttl=600

[session]
# Prevents javascript XSS attacks aimed to steal the session ID
session.cookie_httponly = 0
# Force HTTPS usage if possible
session.cookie_secure = 0
# Disable pass sessionid through URL
session.use_only_cookies = 0
# Allow cookie usage from GET
session.cookie_samesite="Lax"

[symfony]
expose_php=off
