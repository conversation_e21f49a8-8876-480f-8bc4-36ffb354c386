#!/bin/sh

apk add git bash curl

# Install Symfony CLI
curl -1sLf 'https://dl.cloudsmith.io/public/symfony/stable/setup.alpine.sh' | bash
apk add symfony-cli

GIT_AUTHOR_NAME='IT-ROOM' EMAIL='<EMAIL>' symfony new --version=lts /tmp/new-project
rm -rf /tmp/new-project/docker-compose.* /tmp/new-project/compose.*

cd /tmp/new-project || exit

# Install webpack
composer require -n symfony/orm-pack symfony/webpack-encore-bundle

# Install grumphp
curl https://gist.githubusercontent.com/cogirard/23aab1354d06ae550017330e5920dd6e/raw/grumphp.yml -o grumphp.yml

composer config --no-plugins allow-plugins.phpro/grumphp true
composer config --no-plugins allow-plugins.phpstan/extension-installer true
composer require -n --dev phpstan/phpstan phpstan/extension-installer phpstan/phpstan-doctrine phpro/grumphp orm-fixtures friendsofphp/php-cs-fixer povils/phpmnd

cp -rT /tmp/new-project /app

cd /app || exit

# Génération de la chaine de connexion par défaut
# shellcheck disable=SC2046
if [ -f infra/.env ]; then

  if ! grep -q '^MYSQL_PASSWORD=' 'infra/.env'; then
    printf 'MYSQL_PASSWORD=%s\n' $(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 16 | head -n 1) >>infra/.env
  fi
  if ! grep -q '^MYSQL_ROOT_PASSWORD=' 'infra/.env'; then
    printf 'MYSQL_ROOT_PASSWORD=%s\n' $(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 16 | head -n 1) >>infra/.env
  fi
  
  export $(grep -v '^#' infra/.env | xargs)
  echo "DATABASE_URL=\"mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@mysql:3306/${MYSQL_DATABASE}?serverVersion=8.0.32&charset=utf8mb4\"" >>.env
fi
