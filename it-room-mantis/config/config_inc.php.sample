<?php
$g_hostname               = 'mysql';
$g_db_type                = 'mysqli';
$g_database_name          = 'mantis';
$g_db_username            = 'mantis';
$g_db_password            = 'mantis';

$g_default_timezone       = 'UTC';


#NouveauWorkflowMantis
$g_status_enum_string='10:new,20:feedback,50:assigned,80:resolved,81:delivered,82:production,90:closed';

#Définitiondeschangementsdestatutspourlesstatutsstandards
$g_status_enum_workflow[NEW_]='20:feedback,50:assigned,80:resolved';
$g_status_enum_workflow[FEEDBACK] ='10:new,50:assigned,81:delivered,82:production,80:resolved';
$g_status_enum_workflow[ASSIGNED] ='20:feedback,80:resolved,81:delivered,82:production,90:closed';
$g_status_enum_workflow[RESOLVED] ='50:assigned,81:delivered,82:production,80:resolved,90:closed';
$g_status_enum_workflow[CLOSED] ='50:assigned,81:delivered,82:production,80:resolved';

#Définitiondescouleursdesnouveauxstatuts
$g_status_colors['delivered']='#F3E2A9';
$g_status_colors['production']='#E0701A';

#Definitiondeschangementsdestatutspossiblespournosnouveauxstatuts
$g_status_enum_workflow[DELIVERED]='20:feedback,50:assigned,80:resolved,81:delivered,82:production,90:closed';
$g_status_enum_workflow[PRODUCTION]='20:feedback,50:assigned,80:resolved,81:delivered,82:production,90:closed';

#Paramètrespourlapagemy_views.php
$g_bug_delivered_status_threshold=DELIVERED;
$g_bug_production_status_threshold=PRODUCTION;
$g_crypto_master_salt="XvBwLqNmZaPsDfTh";

$g_show_avatar= ON;
$g_show_avatar_threshold = REPORTER;
$g_show_user_email_threshold = REPORTER;
$g_show_user_realname_threshold = REPORTER;

$g_logo_image='/images/logo-itroom.png';

$g_phpMailer_method = PHPMAILER_METHOD_SMTP;
$g_smtp_host = '127.0.0.1';
$g_allow_signup= OFF;

#Configuration des emails
$g_administrator_email = '<EMAIL>';
$g_webmaster_email = '<EMAIL>';
$g_from_email = '<EMAIL>';
$g_return_path_email = '<EMAIL>'; 

#Modification du titre
$g_window_title = "Mantis IT-Room";

# Permettre aux rapporteurs de fermer leurs propres tickets après résolution
$g_allow_reporter_close = ON;

# Configuration anti-spam - augmenter les limites pour les utilisateurs légitimes
$g_antispam_max_event_count = 50;
$g_antispam_time_window_in_seconds = 3600;
