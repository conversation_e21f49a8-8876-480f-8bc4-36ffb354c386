<?php
# MantisBT - A PHP based bugtracking system

# MantisBT is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 2 of the License, or
# (at your option) any later version.
#
# MantisBT is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with MantisBT.  If not, see <http://www.gnu.org/licenses/>.

/**
 * A webservice interface to Mantis Bug Tracker
 *
 * @package MantisBT
 * @copyright Copyright 2004  <PERSON> - v<PERSON>@users.sourceforge.net
 * @copyright Copyright 2005  MantisBT Team - <EMAIL>
 * @link http://www.mantisbt.org
 */

/**
 * Get the value for the specified user preference.
 *
 * @param string  $p_username   The user's username.
 * @param string  $p_password   The user's password.
 * @param integer $p_project_id Project ID (0 = ALL_PROJECTS (mantisbt/core/constant_inc.php)).
 * @param string  $p_pref_name  The name of the preference.
 * @return string $t_user_pref  The requested preference value.
 */
function mc_user_pref_get_pref( $p_username, $p_password, $p_project_id, $p_pref_name ) {
	$t_user_id = mci_check_login( $p_username, $p_password );
	if( $t_user_id === false ) {
		return mci_fault_login_failed();
	}

	if( !mci_has_readonly_access( $t_user_id ) ) {
		return mci_fault_access_denied( $t_user_id );
	}

	return user_pref_get_pref( $t_user_id, $p_pref_name, $p_project_id );
}
