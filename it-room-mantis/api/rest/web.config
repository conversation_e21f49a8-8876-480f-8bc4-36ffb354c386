<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="MantisBT REST API rewrite rule" stopProcessing="true">
          <match url="^" ignoreCase="false" />
          <conditions>
            <!--# Based on Slim Framework recommendation @ http://docs.slimframework.com/routing/rewrite/-->
            <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
          </conditions>
          <action type="Rewrite" url="index.php" appendQueryString="true" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
  <location allowOverride="true" />  
</configuration>
