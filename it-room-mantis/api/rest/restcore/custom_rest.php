<?php

use Mantis\Exceptions\MantisException;
use \Slim\Http\Request;
use \Slim\Http\Response;
require_once('issues_rest.php');

$g_app->group('/custom', function() use ( $g_app ) {
	$g_app->get( '/issues', 'rest_custom_issues_get' );
    $g_app->get( '/issues/count', 'rest_custom_issues_count' );
});;

$allowedSortsAndFilters = [
    'id',
    'summary',
    'priority',
    'severity',
    'status',
    'last_updated',
    'handler_id',
    'reporter_id',
];

function rest_custom_issues_count( Request $p_request, Response $p_response, array $p_args ) {
    $t_filter_id = trim( $p_request->getParam( 'filter_id', FILTER_STANDARD_ANY ) );
    $t_project_id = (int)$p_request->getParam( 'project_id', ALL_PROJECTS );
    $t_user_id = mci_check_login( '', '' );
    if( $t_user_id === false ) {
        return mci_fault_login_failed();
    }

    if( $t_project_id != ALL_PROJECTS && !project_exists( $t_project_id ) ) {
        $t_message = "Project '$t_project_id' doesn't exist";
        return $p_response->withStatus( HTTP_STATUS_NOT_FOUND, $t_message );
    } else {

        if( is_numeric( $t_filter_id ) ) {
            $t_filter = filter_get( $t_filter_id );
        } else {
            $t_filter = filter_standard_get( $t_filter_id, $t_user_id, $t_project_id );
        }
        $t_page_count = 0;
        $t_bug_count = 0;

        filter_get_bug_rows(
            $p_page_number,
            $p_per_page,
            $t_page_count,
            $t_bug_count,
            $t_filter,
            $t_project_id,
            $t_user_id,
            false );

        return $p_response->withJson( [ 'count' => $t_bug_count, 'page_count'=> $t_page_count ] );
    }
}

function rest_custom_issues_get( Request $p_request, Response $p_response, array $p_args ) {
    global $allowedSortsAndFilters;

    $sort = $p_request->getParam('sort','last_updated:desc');
    $sort = explode(':', $sort);
    if (!in_array($sort[0], $allowedSortsAndFilters)) {
        return $p_response->withStatus(400)->withJson(['error' => 'Invalid sort field, allowed fields are: ' . implode(', ', $allowedSortsAndFilters)]);

    }
    
    $sort[1] = strtoupper($sort[1] ?? 'ASC');
    if (!in_array($sort[1], ['ASC', 'DESC'])) {
        return $p_response->withStatus(400)->withJson(['error' => 'Invalid sort field, allowed fields are: ASC, DESC']);
    }

    $sortField = $sort[0];
    $sortOrder = $sort[1];
    
    $p_filter_id = trim( $p_request->getParam( 'filter_id', '' ) );
    if (empty($p_filter_id)) {
        return $p_response->withStatus(400)->withJson(['error' => 'filter_id is required']);
    }
    if (!is_numeric($p_filter_id)) {
        return $p_response->withStatus(400)->withJson(['error' => 'filter_id must be a number']);
    }

    $t_filter = filter_get( $p_filter_id );
    if ( !$t_filter ) {
        return $p_response->withStatus(404)->withJson(['error' => 'Filter not found']);
    }

    $filterName = 'Filtre ' . $p_filter_id . ' ' . $sortField . '_' . $sortOrder;

    $filterName = rtrim($filterName, '_');
    
    $t_filter['sort'] = $sortField;
    $t_filter['dir'] = $sortOrder;
    
    $t_user_id = mci_check_login( '', '' );
    if ( !$t_user_id ) {
        return $p_response->withStatus(401)->withJson(['error' => 'Unauthorized']);
    }

    $sql = 'SELECT id FROM mantis_filters_table WHERE user_id= ' . db_param() . ' AND  name = ' . db_param();
    $t_result = db_query($sql, [$t_user_id, $filterName]);
    $filter = db_fetch_array( $t_result );
    
    if ($filter){
        filter_db_update_filter( $filter['id'], filter_serialize( $t_filter ), null, 0, $filterName);
    }else{
        filter_db_create_filter(filter_serialize( $t_filter ),$t_user_id,0, $filterName,false);
    }

    $params = $p_request->getQueryParams();
    $params['filter_id'] = $filter['id'];
    $p_request = $p_request->withQueryParams($params);


    return rest_issue_get( $p_request, $p_response, $p_args );
}
