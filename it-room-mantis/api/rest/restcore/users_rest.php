<?php
# MantisBT - A PHP based bugtracking system

# MantisBT is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 2 of the License, or
# (at your option) any later version.
#
# MantisBT is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with MantisBT.  If not, see <http://www.gnu.org/licenses/>.

/**
 * A webservice interface to Mantis Bug Tracker
 *
 * @package MantisBT
 * @copyright Copyright MantisBT Team - <EMAIL>
 * @link http://www.mantisbt.org
 */

$g_app->group('/users', function() use ( $g_app ) {
	$g_app->get( '', 'rest_user_get_all' );
	$g_app->get( '/me', 'rest_user_get_me' );

	$g_app->post( '/', 'rest_user_create' );
	$g_app->post( '', 'rest_user_create' );

	$g_app->delete( '/{id}', 'rest_user_delete' );
	$g_app->delete( '/{id}/', 'rest_user_delete' );
});

/**
 * A method that does the work to get information about current logged in user.
 *
 * @param \Slim\Http\Request $p_request   The request.
 * @param \Slim\Http\Response $p_response The response.
 * @param array $p_args Arguments
 * @return \Slim\Http\Response The augmented response.
 */
function rest_user_get_me( \Slim\Http\Request $p_request, \Slim\Http\Response $p_response, array $p_args ) {
	$t_result = mci_user_get( auth_get_current_user_id() );
	return $p_response->withStatus( HTTP_STATUS_SUCCESS )->withJson( $t_result );
}

/**
 * A method that retrieves the list of all users who have access to the same projects as the current user and have created at least one issue.
 *
 * @param \Slim\Http\Request $p_request   The request.
 * @param \Slim\Http\Response $p_response The response.
 * @param array $p_args Arguments
 * @return \Slim\Http\Response The augmented response.
 */
function rest_user_get_all( \Slim\Http\Request $p_request, \Slim\Http\Response $p_response, array $p_args ) {
    $t_current_user_id = auth_get_current_user_id();
    $t_project_id = $p_request->getParam('projectId', null);

    // Determine the list of project IDs based on the provided 'projectId' parameter
    $t_project_ids = $t_project_id !== null
        ? user_get_accessible_subprojects($t_current_user_id, $t_project_id)
        : user_get_all_accessible_projects($t_current_user_id);

    // If no subprojects are found for the given project, use the project ID itself
    if ($t_project_id !== null && empty($t_project_ids)) {
        $t_project_ids = array($t_project_id);
    }

    $t_users = array();
    $t_user_bug_counts = user_get_reported_bug_counts_for_projects($t_project_ids);

    foreach ($t_user_bug_counts as $t_user_id => $t_data) {
        if ($t_data['bug_count'] > 0) {
            $t_users[$t_user_id] = array(
                'id' => $t_user_id,
                'name' => $t_data['username'],
                'realName' => $t_data['realname']
            );
        }
    }

    $t_result = array('users' => array_values($t_users));

    return $p_response->withStatus(HTTP_STATUS_SUCCESS)->withJson($t_result);
}

/**
 * Helper function to get reported bug counts for users across multiple projects.
 *
 * @param array $p_project_ids List of project IDs.
 * @return array Associative array of user data with bug counts.
 */
function user_get_reported_bug_counts_for_projects(array $p_project_ids) {
    $t_user_data = array();

    // Query to fetch user data and bug counts in a single operation
    //TODO: Gestion des status
    $t_query = 'SELECT u.id, u.username, u.realname, COUNT(b.id) AS bug_count
                FROM {user} u
                JOIN {bug} b ON b.reporter_id = u.id
                WHERE b.project_id IN (' . implode(',', $p_project_ids) . ')
                  AND b.status != 90
                GROUP BY u.id, u.username, u.realname';
    $t_result = db_query($t_query);

    while ($t_row = db_fetch_array($t_result)) {
        $t_user_data[$t_row['id']] = array(
            'username' => $t_row['username'],
            'realname' => $t_row['realname'],
            'bug_count' => $t_row['bug_count']
        );
    }

    return $t_user_data;
}

/**
 * A method that creates a user.
 *
 * @param \Slim\Http\Request $p_request   The request.
 * @param \Slim\Http\Response $p_response The response.
 * @param array $p_args Arguments
 * @return \Slim\Http\Response The augmented response.
 */
function rest_user_create( \Slim\Http\Request $p_request, \Slim\Http\Response $p_response, array $p_args ) {
	$t_payload = $p_request->getParsedBody();
	if( $t_payload === null ) {
		return $p_response->withStatus( HTTP_STATUS_BAD_REQUEST, "Unable to parse body, specify content type" );
	}

	$t_data = array( 'payload' => $t_payload );
	$t_command = new UserCreateCommand( $t_data );
	$t_result = $t_command->execute();
	$t_user_id = $t_result['id'];

	return $p_response->withStatus( HTTP_STATUS_CREATED, "User created with id $t_user_id" )->
		withJson( array( 'user' => mci_user_get( $t_user_id ) ) );
}

/**
 * Delete an user given its id.
 *
 * @param \Slim\Http\Request $p_request   The request.
 * @param \Slim\Http\Response $p_response The response.
 * @param array $p_args Arguments
 * @return \Slim\Http\Response The augmented response.
 */
function rest_user_delete( \Slim\Http\Request $p_request, \Slim\Http\Response $p_response, array $p_args ) {
	$t_user_id = $p_args['id'];

	$t_data = array(
		'query' => array( 'id' => $t_user_id )
	);

	$t_command = new UserDeleteCommand( $t_data );
	$t_command->execute();

	return $p_response->withStatus( HTTP_STATUS_NO_CONTENT );	
}
