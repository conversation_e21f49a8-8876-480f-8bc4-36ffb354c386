import {Checkbox, FormControlLabel, Icon<PERSON>utton, Stack} from "@mui/material";
import React, { useEffect, useState } from "react";
import IssuesTable from "@/components/issues/table/IssuesTable/IssuesTable";
import {
  IssueResponse,
  IssuesFilter,
  IssuesFiltersAttributes,
  UserBase,
} from "@/types";
import { getIssues } from "@/services/issue";
import { useLoading, useSnackbar } from "@/hooks";
import Button from "@mui/material/Button";
import { useSearchParams } from "react-router-dom";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ProjectSelector from "@/components/selectors/ProjectSelector/ProjectSelector";
import Typography from "@mui/material/Typography";
import { applyUrlParam } from "@/utils/url";
import FilterSelector from "@/components/selectors/FilterSelector/FilterSelector";

export default function HomePage() {
  const [searchParams, setSearchParams] = useSearchParams();

  const { setLoading } = useLoading();
  const { showError } = useSnackbar();

  const [issues, setIssues] = useState<IssueResponse[]>();

  const [filter, setFilter] = useState<IssuesFilter>(new IssuesFilter());

  useEffect(() => {
    const page = searchParams.get("page");
    const size = searchParams.get("size");
    const projectId = searchParams.get("projectId");
    const filterId = searchParams.get("filterId");

    const updatedFilter = new IssuesFilter();

    updatedFilter.page = page ? parseInt(page) : 1;
    updatedFilter.size = size ? parseInt(size) : 50;
    updatedFilter.projectId = projectId ? parseInt(projectId) : null;
    updatedFilter.selfAssignedOnly = searchParams.has("selfAssignedOnly");
    updatedFilter.filterId = filterId ? parseInt(filterId) : null;

    setFilter(updatedFilter);

    if (userIds) {
      getUsers().then((response) => {
        const selected = response.filter((user) => userIds.includes(user.id));
        setSelectedUsers(selected);
      });
    }

    reloadIssues(updatedFilter);
  }, [searchParams]);

  useEffect(() => {
    if (filter.projectId !== null) {
      getUsersByProject(filter.projectId).then((response) => {
        setUserOptions(response);
      });
    } else {
      getUsers().then((response) => {
        setUserOptions(response);
      });
    }
  }, [filter.projectId]);

  function updateUrlParams(updatedFilter: IssuesFiltersAttributes) {
    const updatedSearchParams = Object.assign(searchParams);

    applyUrlParam(updatedSearchParams, "page", updatedFilter.page, filter.page);
    applyUrlParam(updatedSearchParams, "size", updatedFilter.size, filter.size);
    applyUrlParam(
      updatedSearchParams,
      "projectId",
      updatedFilter.projectId,
      filter.projectId,
    );

    applyUrlParam(
      updatedSearchParams,
      "filterId",
      updatedFilter.filterId,
      filter.filterId,
    );

    if (updatedFilter.selfAssignedOnly != undefined) {
      if (updatedFilter.selfAssignedOnly) {
        updatedSearchParams.set("selfAssignedOnly", "1");
      } else {
        updatedSearchParams.delete("selfAssignedOnly");
      }
    }

    setSearchParams(updatedSearchParams);
  }

  function reloadIssues(filter: IssuesFilter) {
    getIssues(
      {
        setLoading,
        showError,
      },
      filter,
    ).then((response) => {
      if (response.issues.length == 0 && filter.page > 1) {
        updateUrlParams({
          page: 1,
        });
        return;
      }

      setIssues(response.issues);
      setIssuesCount(response.count);
    });
  }

  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    order: string,
  ) => {
    updateUrlParams({
      orderBy: order 
    });
  };

  return (
    <>

      <Stack
        alignItems="center"
        spacing={2}
        sx={{ mb: 1, flexDirection: { xs: "column", sm: "row" } }}
      >
        <Stack
          sx={{ display: { xs: "flex", sm: "none" } }}
          direction="row"
          spacing={2}
        >
          <ProjectSelector
            filterMode={true}
            selectedProjectId={filter.projectId}
            setProject={(project) => {
              updateUrlParams({
                projectId: project?.id ?? null,
              });
            }}
          />

          <FilterSelector
            selectedFilterId={filter.filterId}
            setFilter={(filter) => {
              updateUrlParams({
                filterId: filter?.id ?? null,
                selfAssignedOnly: false,
              });
            }}
          />
        </Stack>
        <Box
          sx={{ display: { xs: "none", sm: "flex" } }}
        >
          <ProjectSelector
            filterMode={true}
            selectedProjectId={filter.projectId}
            setProject={(project) => {
              updateUrlParams({
                projectId: project?.id ?? null,
              });
            }}
          />
          <Box mr={2}/>
          <FilterSelector
            selectedFilterId={filter.filterId}
            setFilter={(filter) => {
              updateUrlParams({
                filterId: filter?.id ?? null,
                selfAssignedOnly: false,
              });
            }}
          />
        </Box>

        <TitleSelector
          selectedIssueTitle={filter.summary}
          setIssueTitle={(issue) => {
            updateUrlParams({
              summary: issue?.summary ?? undefined,
            });
          }}
        />

        <UserSelector
          selectedUsers={selectedUsers}
          setUsers={(users) => {
            setSelectedUsers(users);
            updateUrlParams({
              userIds: users.map((user) => user.id),
            });
          }}
          projectId={filter.projectId}
          userOptions={userOptions}
        />

        <FormControlLabel
          control={
            <Checkbox
              checked={filter.selfAssignedOnly}
              onChange={(event) => {
                updateUrlParams({
                  selfAssignedOnly: event.target.checked,
                  filterId: null,
                });
              }}
            />
          }
          label="Assigné à moi uniquement"
        />
      </Stack>


      <IssuesTable issues={issues} onRequestSort={handleRequestSort} />

      <Stack
        direction="row"
        justifyContent="flex-end"
        alignItems="center"
        spacing={2}
        sx={{ mt: 1 }}
      >
        <Button variant={"text"}>{issuesCount == -1 ? "..." : issuesCount} tickets</Button>
        <Box sx={{ flexGrow: 1 }} />

        {filter.page != 1 && (
          <>
            <IconButton onClick={() => updateUrlParams({ page: filter.page + 1 })} sx={{display: {xs: "flex", md: "none"}}}>
              <ArrowBackIosNewIcon fontSize={"small"} />
            </IconButton>
            <Button
              variant="contained"
              sx={{ mr: 2, display: {xs: "none", md: "flex"} }}
              startIcon={<ArrowBackIosNewIcon />}
              onClick={() => updateUrlParams({ page: filter.page - 1 })}
              size={"small"}
            >
              Page Précédente
            </Button>
          </>
        )}

        <Button variant={"text"}>Page {filter.page}</Button>

        {issues?.length == filter.size && (
          <>
            <IconButton onClick={() => updateUrlParams({ page: filter.page + 1 })} sx={{display: {xs: "flex", md: "none"}}}>
              <ArrowForwardIosIcon fontSize={"small"} />
            </IconButton>
            <Button
              variant="contained"
              sx={{ mr: 2, display: {xs: "none", md: "flex"} }}
                        endIcon={<ArrowForwardIosIcon />}
                        onClick={() => updateUrlParams({ page: filter.page + 1 })}
                        size={"small"}
            >
              Page Suivante
            </Button>
          </>
        )}
      </Stack>
    </>
  );
}
