import { Alert, Paper, Stack } from "@mui/material";
import React, { useEffect, useState } from "react";
import { getIssue } from "@/services/issue";
import { useLoading, useSnackbar } from "@/hooks";
import { IssueDetailsResponse } from "@/types";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import EventsTimeline from "@/components/issues/timeline/EventsTimeline/EventsTimeline";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import SummaryBox from "@/components/issues/heading/SummaryBox/SummaryBox";
import Button from "@mui/material/Button";
import { useNavigate, useParams } from "react-router-dom";

export default function IssuePage() {
  const { setLoading } = useLoading();

  const { showError } = useSnackbar();

  const { issueId } = useParams();

  const navigate = useNavigate();

  const [issue, setIssue] = useState<IssueDetailsResponse>();

  const [error, setError] = useState<boolean>(false);

  function reloadIssue(bottomScroll = false) {
    getIssue(parseInt(issueId!), { setLoading, showError })
      .then((response) => setIssue(response))
      .catch((error) => {
        console.log(error);
        setError(true);
      });

    if (bottomScroll) {
      window.scrollTo(0, document.body.scrollHeight);
    }
  }

  useEffect(() => {
    reloadIssue();
  }, [issueId]);

  if (error) {
    return (
      <Alert color="error" icon={<ErrorOutlineIcon />}>
        Erreur : Aucun ticket avec cet identifiant n&apos;a été trouvé
      </Alert>
    );
  }
  return (
    <>
      <Button
        onClick={() => navigate(-1)}
        startIcon={<ArrowBackIosNewIcon />}
        sx={{ alignSelf: "flex-start", mb: 2 }}
      >
        Retour
      </Button>

      <Paper elevation={3} sx={{ padding: 2.5, marginBottom: 3 }}>
        <SummaryBox issue={issue} />
      </Paper>

      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        spacing={2}
        sx={{ mb: 2 }}
      >
        {issueId && issue && (
          <EventsTimeline
            issue={issue}
            reloadIssue={() => {
              reloadIssue(true);
            }}
          />
        )}
      </Stack>
    </>
  );
}
