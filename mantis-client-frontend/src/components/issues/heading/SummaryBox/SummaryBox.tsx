import { Divide<PERSON>, Skeleton, Stack } from "@mui/material";
import Typography from "@mui/material/Typography";
import StatusChip from "@/components/chips/StatusChip/StatusChip";
import Box from "@mui/material/Box";
import { Link } from "react-router-dom";
import { getRelationshipType<PERSON>abel, IssueDetailsResponse } from "@/types";
import AttributesBox from "@/components/issues/heading/AttributesBox/AttributesBox";
import React from "react";
import FormattedText from "@/components/layout/FormattedText/FormattedText";
import AttachmentsBox from "@/components/issues/attachments/AttachmentsBox/AttachmentsBox";

export type SummaryBoxProps = {
  issue?: IssueDetailsResponse;
};

/**
 * Boite affichant le résumé du ticket
 * @param props propriétés du composant
 * @constructor
 */
export default function SummaryBox(props: Readonly<SummaryBoxProps>) {
  const { issue } = props;

  return (
    <Stack direction="column" sx={{ flexGrow: 1 }}>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        spacing={2}
        sx={{ paddingBottom: 1.5 }}
      >
        <Stack
          direction="row"
          justifyContent="flex-start"
          alignItems="center"
          spacing={2}
        >
          {issue ? (
            <Typography variant="h6">
              {}#{issue.id} - {issue.summary}
            </Typography>
          ) : (
            <Skeleton variant="rectangular" width={250} height={32} />
          )}
        </Stack>

        {issue ? (
          <StatusChip status={issue.status} />
        ) : (
          <Skeleton variant="rectangular" width={56} height={24} />
        )}
      </Stack>

      <Divider />

      <Stack
        justifyContent={"space-between"}
        alignItems={"flex-start"}
        sx={{ flexDirection: { xs: "column", md: "row" }}}
      >
        <Stack flexGrow={1}
               sx={{ width: {xs: "100%" , md: "50%" }}}>
          <Box sx={{ pt: 2 }}>
            <Typography variant="body1" sx={{ fontWeight: 600 }}>
              Description
            </Typography>
            <Typography
              variant="body2"
              sx={{
                mt: 1,
                whiteSpace: "pre-line",
                wordBreak: "break-word",
              }}
            >
              {issue ? (
                <FormattedText text={issue.description} />
              ) : (
                <Skeleton variant="rectangular" width={420} height={240} />
              )}
            </Typography>
          </Box>

          {issue?.additionalInformation && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                Informations complémentaires
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  mt: 1,
                  whiteSpace: "pre-line",
                  wordBreak: "break-word",
                }}
              >
                <FormattedText text={issue.additionalInformation} />
              </Typography>
            </Box>
          )}

          {issue?.attachments && issue.attachments.length > 0 && (
            <AttachmentsBox
              attachments={issue.attachments}
              issueId={issue.id}
              small={true}
            />
          )}

          {issue?.relationships && issue.relationships.length > 0 && (
            <Stack sx={{ pt: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                Relations
              </Typography>

              {issue.relationships.map((relationship) => {
                return (
                  <Typography
                    variant="body2"
                    sx={{ mt: 1 }}
                    key={relationship.id}
                  >
                    <Link
                      to={`/issue/${relationship.issue.id}`}
                      style={{
                        textDecoration: "initial",
                        color: "inherit",
                      }}
                    >
                      <b>
                        {getRelationshipTypeLabel(relationship.type)} #
                        {relationship.issue.id}
                      </b>{" "}
                      - {relationship.issue.summary}
                    </Link>
                  </Typography>
                );
              })}
            </Stack>
          )}
        </Stack>

        <AttributesBox issue={issue} />
      </Stack>
    </Stack>
  );
}
