import { useSession } from "@/hooks";
import React from "react";
import { IssueResponse } from "@/types";
import { Paper, Table, TableBody, TableContainer } from "@mui/material";
import IssueRow from "../IssueRow/IssueRow";
import EnhancedTableHead from "./EnhancedTableHead";

export type Props = {
  onRequestSort: (event: React.MouseEvent<unknown>, property: string) => void;
  issues: IssueResponse[];
};

type Order = 'asc' | 'desc';

export default function IssuesTable(props: Props) {
  const [order, setOrder] = React.useState<Order>("asc");
  const [orderBy, setOrderBy] = React.useState<keyof IssueResponse>("status");
  const { issues } = props;
  const { user } = useSession();

  const { onRequestSort } = props;

  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: keyof IssueResponse,
    sort_name: string | null,
  ) => {
    setOrderBy(property);
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    onRequestSort(event, sort_name + ':' + (isAsc ? 'desc' : 'asc'));
  };

  return (
    <TableContainer component={Paper} elevation={1}>
      <Table aria-label="Issues Table" stickyHeader>
        <EnhancedTableHead
          order={order}
          orderBy={orderBy}
          onRequestSort={handleRequestSort}
          rowCount={issues?.length || 0}
        />
        <TableBody>
          {issues &&
            issues?.length > 0 &&
            issues.map((issue: IssueResponse) => {
              return (
                <IssueRow
                  key={issue.id}
                  issue={issue}
                  selfAssigned={user?.id == issue.handler?.id}
                />
              );
            })}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
