import React from "react";
import { IssueResponse } from "@/types";
import {
  TableCell,
  TableHead,
  TableRow,
  TableSortLabel,
  Typography,
} from "@mui/material";

type Order = "asc" | "desc";

interface EnhancedTableProps {
  onRequestSort: (
    event: React.MouseEvent<unknown>,
    property: keyof IssueResponse,
    sort_name: string | null,
  ) => void;
  order: Order;
  orderBy: string;
  rowCount: number;
}

interface HeadCell {
  id: keyof IssueResponse;
  label: string;
  align: "left" | "center" | "right";
  width: string;
  pl?: number;
  sort_name?: string;
}

export const headCells: readonly HeadCell[] = [
  {
    id: "summary",
    label: "Résumé",
    align: "left",
    width: "49%",
  },
  {
    id: "id",
    label: "Référence",
    align: "left",
    width: "5%",
  },
  {
    id: "status",
    label: "Statut",
    align: "left",
    width: "6%",
    pl: 5,
  },
  {
    id: "severity",
    label: "Impact",
    align: "left",
    width: "6%",
    pl: 5,
  },
  {
    id: "priority",
    label: "Priorité",
    align: "left",
    width: "6%",
    pl: 5,
  },
  {
    id: "handler",
    label: "Assigné à",
    align: "center",
    width: "10%",
    sort_name: "handler_id",
  },
  {
    id: "updatedAt",
    label: "Dernière mise à jour",
    align: "center",
    width: "10%",
    sort_name: "last_updated",
  },
    {
      id: 'reporter_id',
      label: 'Créé par',
      align: 'left',
      width: '10%',
    },
];

  export default function EnhancedTableHead(props: EnhancedTableProps) {
    const { order, orderBy, onRequestSort } = props;
    const createSortHandler = (property: keyof IssueResponse, sort_name: string | null) => (event: React.MouseEvent<unknown>) => {
      onRequestSort(event, property, sort_name);
    };
  
    return (
      <TableHead>
        <TableRow>
          {headCells.map((headCell) => (
            <TableCell 
              key={headCell.id} 
              align={headCell.align}
              sortDirection={orderBy === headCell.id ? order : false} 
              sx={{ width: headCell.width }}
            >
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : 'asc'}
                onClick={createSortHandler(headCell.id, headCell.sort_name || headCell.id as string)}
              >
                <Typography variant="body2" sx={{ fontWeight: 600 }}>{headCell.label}</Typography>
              </TableSortLabel>
            </TableCell>
          ))}
        </TableRow>
      </TableHead>
    );
  }