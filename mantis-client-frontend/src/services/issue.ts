import {
  IssueDetailsResponse,
  IssueResponse,
  IssuesFilter,
  ViewContext,
  ViewContextWithSuccess,
} from "@/types";
import { api } from "@/services/api";
import { headCells } from "@/components/issues/table/IssuesTable/EnhancedTableHead";

let cache: IssueResponse[] = [];
let cacheCount: number = -1;
let lastFilter: IssuesFilter | null = null;
let lastUpdate = 0;

export function invalidateIssuesCache() {
  lastUpdate = 0;
}

export interface IssueUpdateRequest {
  message: string;
  files: File[];
  updatedStatus: string | null;
}

export interface IssueCreateRequest {
  projectId: number;
  summary: string;
  description: string;
  priority: string;
  severity: string;
  files: File[];
}

/**
 * Mettre à jour un ticket (ajout commentaire, ajout pièce-jointe, changement de statut, changement d'affecté)
 * @param issueId l'identifiant du ticket
 * @param request les informations à mettre à jour
 * @param context le contexte de la vue
 */
export async function updateIssue(
  issueId: number,
  request: IssueUpdateRequest,
  context: ViewContextWithSuccess,
): Promise<void> {
  context.setLoading(true);

  return api
    .postForm("/issues/" + issueId, request)
    .then(() => {
      context.showSuccess("Le ticket a été mis à jour avec succès.");
      return Promise.resolve();
    })
    .catch((error: Error) => {
      context.showError("Erreur : " + error.message);
      return Promise.reject(error);
    })
    .finally(() => context.setLoading(false));
}

/**
 * Créer un ticket
 * @param request les informations du ticket
 * @param context le contexte de la vue
 */
export async function createIssue(
  request: IssueCreateRequest,
  context: ViewContextWithSuccess,
): Promise<IssueResponse> {
  context.setLoading(true);

  return api
    .postForm("/issues", request)
    .then((axiosResponse) => {
      context.showSuccess("Votre ticket a bien été créé. Nous reviendrons vers vous rapidement.");
      return axiosResponse.data as IssueResponse;
    })
    .catch((error: Error) => {
      context.showError("Erreur : " + error.message);
      return Promise.reject(error);
    })
    .finally(() => context.setLoading(false));
}

/**
 * Récupérer un ticket par son identifiant
 * @param id l'identifiant du ticket
 * @param context le contexte de la vue
 */
export async function getIssue(
  id: number,
  context: ViewContext,
): Promise<IssueDetailsResponse> {
  context.setLoading(true);

  return api
    .get("/issues/" + id)
    .then((axiosResponse) => axiosResponse.data as IssueDetailsResponse)
    .catch((error: Error) => {
      return Promise.reject(error);
    })
    .finally(() => context.setLoading(false));
}

/**
 * Récupérer la liste des tickets
 * @param context le contexte de la vue
 * @param filter le filtre à appliquer
 * @param force forcer le rechargement
 */
export async function getIssues(
  context: ViewContext,
  filter: IssuesFilter,
  force?: boolean,
): Promise<{count: number, issues: IssueResponse[]}> {
  if (
    lastFilter != null &&
    filter.getKey() == lastFilter.getKey() &&
    !force &&
    Date.now() - lastUpdate < 1000 * 300
  ) {
    return {count: cacheCount, issues: cache};
  }

  context.setLoading(true);

  let url = "/issues?page=" + filter.page + "&size=" + filter.size;

  if (filter.projectId) {
    url += "&projectId=" + filter.projectId;
  }

  if (filter.selfAssignedOnly) {
    url += "&selfAssignedOnly=true";
  }

  if (filter.filterId) {
    url += "&filterId=" + filter.filterId;
  }

  if (filter.orderBy) {
    const sort_name = filter.orderBy.split(':')[0] ?? null;
    if (headCells.some((headCell) => (headCell.sort_name ?? headCell.id) === sort_name)) {
      url += "&sort=" + filter.orderBy;
    }

  }

  if (filter.summary) {
    url += "&summary=" + filter.summary;
  }

  return api
    .get(url)
    .then((axiosResponse) => {
      cache = axiosResponse.data as IssueResponse[];
      if (
        lastFilter != null &&
        filter.getKey() == lastFilter.getKey() &&
        !force &&
        Date.now() - lastUpdate < 1000 * 300
      ) {
        return {count: cacheCount, issues: cache};
      }

      let url = "/issues/count?page=1&size=-1";

      if (filter.projectId) {
        url += "&projectId=" + filter.projectId;
      }

      if (filter.selfAssignedOnly) {
        url += "&selfAssignedOnly=true";
      }

      if (filter.filterId) {
        url += "&filterId=" + filter.filterId;
      }

      if (filter.orderBy) {
        const sort_name = filter.orderBy.split(':')[0] ?? null;
        if (headCells.some((headCell) => (headCell.sort_name ?? headCell.id) === sort_name)) {
          url += "&sort=" + filter.orderBy;
        }

      }

      return api
        .get(url)
        .then((axiosResponse) => {
          cacheCount = axiosResponse.data.count;
          lastUpdate = Date.now();
          lastFilter = filter;
          return {count: cacheCount, issues: cache};
        })
        .catch((error: Error) => {
          context.showError("Impossible de charger les tickets : " + error.message);
          return {count: 0, issues: []};
        })
        .finally(() => context.setLoading(false));
    })
    .catch((error: Error) => {
      context.showError("Impossible de charger les tickets : " + error.message);
      return {count: 0, issues: []};
    })
    .finally(() => context.setLoading(false));
}
