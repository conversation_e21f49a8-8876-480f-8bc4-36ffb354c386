import { SessionUser, ViewContext } from "@/types";
import { api } from "@/services/api";

export type LoginRequest = {
  username: string;
  password: string;
};

export type ForgotPasswordRequest = {
  username: string;
  email: string;
};

export type ResetPasswordRequest = {
  userId: number;
  token: string;
  password: string;
};

/**
 * Lancer la modification du mot de passe
 * @param request requête contenant le nouvel mot de passe et le token
 * @param context contexte de la vue
 */
export async function resetPassword(
  request: ResetPasswordRequest,
  context: ViewContext,
): Promise<void> {
  context.setLoading(true);

  return api
    .post("/auth/forgot-password/reset", request)
    .then(() => {
      return Promise.resolve();
    })
    .catch((error: Error) => {
      return Promise.reject(error);
    })
    .finally(() => context.setLoading(false));
}

/**
 * Lancer la procédure de réinitialisation de mot de passe
 * @param request données du compte (username et email)
 * @param context contexte de la vue
 */
export async function forgotPassword(
  request: ForgotPasswordRequest,
  context: ViewContext,
): Promise<void> {
  context.setLoading(true);

  return api
    .post("/auth/forgot-password", request)
    .then(() => {
      return Promise.resolve();
    })
    .catch((error: Error) => {
      context.showError(
        "Echec de la demande de réinitialisation de mot de passe, veuillez réessayer ultérieurement.",
      );
      return Promise.reject(error);
    })
    .finally(() => context.setLoading(false));
}

/**
 * Connexion de l'utilisateur
 * @param request données de connexion
 * @param context contexte de la vue
 */
export async function login(
  request: LoginRequest,
  context: ViewContext,
): Promise<void> {
  context.setLoading(true);

  return api
    .post("/auth/login", request)
    .then(() => {
      return Promise.resolve();
    })
    .catch((error: Error) => {
      context.showError(
        "Echec de la connexion, veuillez vérifier vos identifiants.",
      );
      return Promise.reject(error);
    })
    .finally(() => context.setLoading(false));
}

/**
 * Déconnexion de l'utilisateur
 */
export async function logout(): Promise<void> {
  return api
    .get("/auth/logout")
    .then(() => {
      return Promise.resolve();
    })
    .catch((error: Error) => {
      console.error(error);
      return Promise.reject(error);
    });
}

/**
 * Récupérer les informations de l'utilisateur connecté
 */
export async function getCurrentUser(
  context: ViewContext,
): Promise<SessionUser> {
  context.setLoading(true);

  return api
    .get("/auth/me")
    .then((axiosResponse) => {
      return axiosResponse.data as SessionUser;
    })
    .catch((error: Error) => {
      return Promise.reject(error);
    })
    .finally(() => context.setLoading(false));
}
