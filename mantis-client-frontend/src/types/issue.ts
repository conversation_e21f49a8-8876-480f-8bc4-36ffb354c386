import { ProjectBase } from "@/types/project";
import { UserBase } from "@/types/user";
import { AttachmentEvent, EventStack } from "@/types/event";
import { Priority, Severity, Status, ViewState } from "@/types/attribute";

export interface IssueBase {
  id: number;
  summary: string;
  status: Status;
}

export interface IssueResponse extends IssueBase {
  description: string;
  additionalInformation: string;
  project: ProjectBase;
  category?: Category;
  reporter: UserBase;
  handler?: UserBase;
  priority: Priority;
  severity: Severity;
  createdAt: string;
  updatedAt: string;
}

export interface IssueDetailsResponse extends IssueResponse {
  viewState: ViewState;
  attachments: AttachmentEvent[];
  events: EventStack[];
  relationships: Relationship[];
  allowHandlerUpdate: boolean;
  allowedStatus: Status[];
}

export interface Relationship {
  id: number;
  type: RelationshipType;
  issue: IssueBase;
}

export const enum RelationshipType {
  BUG_DUPLICATE = "BUG_DUPLICATE",
  BUG_RELATED = "BUG_RELATED",
  BUG_DEPENDANT = "BUG_DEPENDANT",
  BUG_BLOCKED = "BUG_BLOCKED",
  BUG_HAS_DUPLICATE = "BUG_HAS_DUPLICATE",
}

export function getRelationshipTypeLabel(type: RelationshipType) {
  switch (type) {
    case RelationshipType.BUG_DUPLICATE:
      return "doublon de";
    case RelationshipType.BUG_RELATED:
      return "relatif à";
    case RelationshipType.BUG_DEPENDANT:
      return "dépendant de";
    case RelationshipType.BUG_BLOCKED:
      return "enfant de";
    case RelationshipType.BUG_HAS_DUPLICATE:
      return "à pour doublon";
    default:
      return "Inconnu";
  }
}

export interface Category {
  id: number;
  name: string;
}

export interface IssueSpecifications {
  projectId: number | null;
  summary: string;
  description: string;
  priority: Priority | null;
  severity: Severity | null;
  files: File[];
}

export interface IssueUpdateSpecifications {
  message: string;
  files: File[];
}

export interface IssuesFiltersAttributes {
  page?: number;
  size?: number;
  projectId?: number | null;
  selfAssignedOnly?: boolean;
  filterId?: number | null;
  orderBy?: string | null;
  summary?: string | undefined;

}

function stringToHash(str: string) {
  let hash = 5381;
  for (let i = 0; i < str.length; i++) {
      hash = (hash * 33) ^ str.charCodeAt(i);
  }
  return hash >>> 0; // Convert to unsigned 32-bit integer
}

export class IssuesFilter implements IssuesFiltersAttributes {
  page: number;
  size: number;
  projectId: number | null;
  selfAssignedOnly: boolean;
  filterId: number | null;
  orderBy: string | null;
  summary: string | undefined;

  constructor() {
    this.page = 1;
    this.size = 20;
    this.projectId = null;
    this.selfAssignedOnly = false;
    this.filterId = null;
    this.orderBy = null;
    this.summary = undefined;
  }

  getKey = (): number => {
    return (
      this.page * 10 +
      this.size * 100 +
      (this.projectId ?? 0) * 1000 +
      (this.selfAssignedOnly ? 1 : 0) * 10000 +
      (this.filterId ?? 0) * 100000 +
      stringToHash(this.orderBy ?? "") * 1000000 +
      (this.summary ? 10000000 : 0)
    );
  };
}
